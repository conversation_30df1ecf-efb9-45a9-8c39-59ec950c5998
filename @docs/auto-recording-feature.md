# Tính năng Tự động Ghi âm Cuộc gọi

## Tổng quan

Tính năng tự động ghi âm cho phép hệ thống call center tự động bắt đầu ghi âm ngay khi cuộc gọi được kết nối, khô<PERSON> cần can thiệp thủ công từ nhân viên.

## Tính năng chính

### 🔴 Ghi âm Tự động
- **Tự động bắt đầu**: Ghi âm bắt đầu tự động sau 500ms khi cuộc gọi kết nối
- **Tự động dừng**: Ghi âm dừng tự động khi cuộc gọi kết thúc
- **Không cần can thiệp**: Nhân viên không cần nhấn nút ghi âm

### ⚙️ Cài đặt Linh hoạt
- **Bật/Tắt**: <PERSON><PERSON> thể bật/tắt tự động ghi âm qua giao diện
- **Ghi âm thủ công**: Vẫn có thể ghi âm thủ công khi tắt tự động
- **Hiển thị trạng thái**: Giao diện hiển thị rõ trạng thái ghi âm

### 📁 Quản lý File
- **Định dạng**: File ghi âm được lưu dưới định dạng WebM (Opus codec)
- **Tên file**: Tự động tạo tên file với timestamp và số điện thoại
- **Kích thước**: Hiển thị kích thước file và thời lượng ghi âm

## Cách sử dụng

### 1. Cài đặt Tự động Ghi âm

Khi không có cuộc gọi nào đang diễn ra, bạn sẽ thấy phần cài đặt:

```
┌─────────────────────────────────────────┐
│ ⚙️ Cài đặt ghi âm                      │
│                                         │
│ Tự động ghi âm              [🔘 BẬT]   │
│                                         │
│ 🔴 Ghi âm sẽ tự động bắt đầu khi       │
│    cuộc gọi được kết nối                │
└─────────────────────────────────────────┘
```

- **BẬT**: Ghi âm tự động bắt đầu khi cuộc gọi kết nối
- **TẮT**: Cần nhấn nút "Ghi âm" thủ công

### 2. Thực hiện Cuộc gọi với Ghi âm Tự động

#### Cuộc gọi đi:
1. Nhập số điện thoại
2. Nhấn "Gọi"
3. Khi cuộc gọi kết nối → **🔴 Ghi âm tự động bắt đầu**
4. Thực hiện cuộc gọi bình thường
5. Kết thúc cuộc gọi → **⏹️ Ghi âm tự động dừng**

#### Cuộc gọi đến:
1. Có cuộc gọi đến
2. Nhấn "Trả lời"
3. Khi trả lời → **🔴 Ghi âm tự động bắt đầu**
4. Thực hiện cuộc gọi bình thường
5. Kết thúc cuộc gọi → **⏹️ Ghi âm tự động dừng**

### 3. Hiển thị Trạng thái Ghi âm

Trong cuộc gọi, bạn sẽ thấy:

```
┌─────────────────────────────────────────┐
│ 📞 Nguyễn Văn A (0987654321)           │
│ 🔴 Đang ghi âm                 ⏱️ 02:15 │
└─────────────────────────────────────────┘
```

### 4. Điều khiển Ghi âm Thủ công

Ngay cả khi bật tự động ghi âm, bạn vẫn có thể:
- **Dừng ghi âm**: Nhấn nút "Dừng ghi âm"
- **Bắt đầu lại**: Nhấn nút "Ghi âm" để bắt đầu lại

```
[Kết thúc] [Tắt mic] [Dừng ghi âm (Auto)]
```

## Cấu hình Kỹ thuật

### Định dạng File
- **Codec**: Opus (chất lượng cao, kích thước nhỏ)
- **Container**: WebM
- **Bitrate**: 64 kbps
- **Sample Rate**: 48 kHz

### Giới hạn
- **Thời lượng tối đa**: 1 giờ/cuộc gọi
- **Kích thước tối đa**: 50MB/file
- **Delay bắt đầu**: 500ms sau khi kết nối

### Tên File
Format: `call-recording-{timestamp}-{phone}.webm`

Ví dụ: `call-recording-1705312200000-0987654321.webm`

## Tương thích Trình duyệt

### Được hỗ trợ đầy đủ:
- ✅ Chrome 47+
- ✅ Firefox 25+
- ✅ Edge 79+
- ✅ Safari 14.1+

### Hỗ trợ hạn chế:
- ⚠️ Safari cũ (< 14.1): Không hỗ trợ Opus codec
- ⚠️ Internet Explorer: Không hỗ trợ

## Xử lý Lỗi

### Lỗi thường gặp:

1. **"Trình duyệt không hỗ trợ ghi âm"**
   - Sử dụng Chrome, Firefox hoặc Edge mới nhất
   - Cập nhật trình duyệt lên phiên bản mới

2. **"Không có stream âm thanh để ghi âm"**
   - Kiểm tra quyền microphone
   - Thử refresh trang và cấp quyền lại

3. **"Lỗi khi lưu file ghi âm"**
   - Kiểm tra dung lượng ổ cứng
   - Thử ghi âm với thời lượng ngắn hơn

## Console Logs

Khi ghi âm hoạt động, bạn sẽ thấy các log sau trong Console:

```
🔴 Tự động bắt đầu ghi âm cuộc gọi
💾 File ghi âm đã được lưu: call-recording-1705312200000-0987654321.webm (2.1 MB, 180s)
⏹️ Tự động dừng ghi âm cuộc gọi
```

## Lưu ý Quan trọng

### Pháp lý:
- ⚠️ **Thông báo cho khách hàng** về việc ghi âm cuộc gọi
- ⚠️ **Tuân thủ quy định** về bảo mật dữ liệu cá nhân
- ⚠️ **Lưu trữ an toàn** file ghi âm

### Kỹ thuật:
- 🔧 File hiện tại chỉ được lưu local (cần tích hợp upload server)
- 🔧 Cần implement API backend để lưu trữ và quản lý file
- 🔧 Nên có cơ chế backup và recovery cho file ghi âm

## Roadmap

### Phase tiếp theo:
1. **Upload tự động** file lên server
2. **Trang quản lý** lịch sử ghi âm
3. **Phát lại** file ghi âm trong giao diện
4. **Tìm kiếm** và **filter** file ghi âm
5. **Xuất báo cáo** thống kê ghi âm

### Tính năng nâng cao:
- **Transcription**: Chuyển đổi giọng nói thành văn bản
- **AI Analysis**: Phân tích cảm xúc và từ khóa
- **Cloud Storage**: Lưu trữ trên cloud (AWS S3, Google Cloud)
- **Compression**: Nén file để tiết kiệm dung lượng
