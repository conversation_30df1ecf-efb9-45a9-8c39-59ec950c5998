# Tài liệu triển khai tính năng ghi âm cuộc gọi

## Tổng quan

Tài liệu này mô tả các bước cần thực hiện để triển khai tính năng ghi âm cuộc gọi hoàn chỉnh trong hệ thống call center, bao gồm ghi âm thực tế, lưu trữ file, và quản lý lịch sử cuộc gọi.

## 1. C<PERSON>u trúc tổng thể

### 1.1 Luồng hoạt động
```mermaid
graph TD
    A[Bắt đầu cuộc gọi] --> B[Nhấn nút Ghi âm]
    B --> C[MediaRecorder bắt đầu ghi]
    C --> D[Cuộc gọi diễn ra]
    D --> E[Nhấn Dừng ghi âm / Kết thúc cuộc gọi]
    E --> F[Tạo file audio blob]
    F --> G[Upload file lên server]
    G --> H[<PERSON><PERSON><PERSON> thông tin cuộc gọi + recordingUrl vào DB]
    H --> I[Hiển thị trong lịch sử cuộc gọi]
```

### 1.2 C<PERSON><PERSON> thành phần cần cập nhật
- **Frontend**: `sipService.ts`, `CallInterface.tsx`, trang lịch sử cuộc gọi
- **Backend**: API endpoints cho cuộc gọi, file upload, lịch sử
- **Database**: Bảng calls với trường recordingUrl

## 2. Backend APIs cần triển khai

### 2.1 API quản lý cuộc gọi

#### **POST /api/calls**
Tạo record cuộc gọi mới khi bắt đầu cuộc gọi

**Request Body:**
```json
{
  "customerPhone": "0987654321",
  "direction": "inbound" | "outbound",
  "adminId": "admin_id",
  "startTime": "2024-01-15T10:30:00Z"
}
```

**Response:**
```json
{
  "id": "call_123",
  "customerPhone": "0987654321",
  "direction": "inbound",
  "adminId": "admin_id",
  "adminName": "Nguyễn Văn A",
  "status": "ongoing",
  "startTime": "2024-01-15T10:30:00Z",
  "recordingUrl": null
}
```

#### **PUT /api/calls/:id**
Cập nhật thông tin cuộc gọi (khi kết thúc, thêm ghi chú, etc.)

**Request Body:**
```json
{
  "status": "completed",
  "endTime": "2024-01-15T10:35:00Z",
  "duration": 300,
  "notes": "Khách hàng hỏi về sản phẩm X",
  "recordingUrl": "file_id_from_upload"
}
```

#### **GET /api/calls**
Lấy danh sách cuộc gọi có phân trang và filter

**Query Parameters:**
```
?page=1&limit=20&status=completed&direction=inbound&startDate=2024-01-01&endDate=2024-01-31&search=0987654321
```

**Response:**
```json
{
  "data": [
    {
      "id": "call_123",
      "customerId": "customer_456",
      "customerName": "Nguyễn Thị B",
      "customerPhone": "0987654321",
      "adminId": "admin_789",
      "adminName": "Nguyễn Văn A",
      "status": "completed",
      "direction": "inbound",
      "duration": 300,
      "notes": "Khách hàng hỏi về sản phẩm X",
      "recordingUrl": "file_abc123",
      "startTime": "2024-01-15T10:30:00Z",
      "endTime": "2024-01-15T10:35:00Z"
    }
  ],
  "total": 150,
  "page": 1,
  "limit": 20,
  "totalPages": 8
}
```

#### **GET /api/calls/:id**
Lấy chi tiết một cuộc gọi

**Response:**
```json
{
  "id": "call_123",
  "customerId": "customer_456",
  "customerName": "Nguyễn Thị B",
  "customerPhone": "0987654321",
  "adminId": "admin_789",
  "adminName": "Nguyễn Văn A",
  "status": "completed",
  "direction": "inbound",
  "duration": 300,
  "notes": "Khách hàng hỏi về sản phẩm X",
  "recordingUrl": "file_abc123",
  "startTime": "2024-01-15T10:30:00Z",
  "endTime": "2024-01-15T10:35:00Z"
}
```

#### **DELETE /api/calls/:id**
Xóa record cuộc gọi (chỉ admin)

**Response:**
```json
{
  "success": true,
  "message": "Đã xóa cuộc gọi thành công"
}
```

### 2.2 API quản lý file ghi âm

#### **POST /upload/file** (Đã có sẵn)
Upload file ghi âm lên server

**Request:** `multipart/form-data` với file audio

**Response:**
```json
{
  "_id": "file_abc123",
  "ownerId": "admin_789",
  "name": "call-recording-1705312200000.webm",
  "displayName": "Ghi âm cuộc gọi 15/01/2024 10:30",
  "fileType": "audio",
  "mimetype": "audio/webm",
  "size": "2048576",
  "storageType": "local",
  "storageLocation": "/uploads/recordings/",
  "createdAt": "2024-01-15T10:35:00Z",
  "updatedAt": "2024-01-15T10:35:00Z"
}
```

#### **GET /api/files/content/:fileId** (Đã có sẵn)
Lấy nội dung file để phát hoặc tải về

**Response:** Stream file audio

#### **GET /api/files/:fileId** (Đã có sẵn)
Lấy metadata của file

### 2.3 API thống kê cuộc gọi

#### **GET /api/calls/stats**
Lấy thống kê tổng quan về cuộc gọi

**Query Parameters:**
```
?startDate=2024-01-01&endDate=2024-01-31&adminId=admin_789
```

**Response:**
```json
{
  "totalCalls": 150,
  "answeredCalls": 120,
  "missedCalls": 30,
  "averageDuration": 180,
  "totalDuration": 21600,
  "recordedCalls": 100,
  "callsByDirection": {
    "inbound": 90,
    "outbound": 60
  },
  "callsByStatus": {
    "completed": 120,
    "missed": 30
  },
  "callsByHour": [
    { "hour": 8, "count": 5 },
    { "hour": 9, "count": 12 },
    { "hour": 10, "count": 18 }
  ]
}
```

## 3. Database Schema

### 3.1 Bảng calls
```sql
CREATE TABLE calls (
  id VARCHAR(36) PRIMARY KEY,
  customer_id VARCHAR(36),
  customer_name VARCHAR(255),
  customer_phone VARCHAR(20) NOT NULL,
  admin_id VARCHAR(36),
  admin_name VARCHAR(255),
  status ENUM('missed', 'answered', 'ongoing', 'completed') NOT NULL,
  direction ENUM('inbound', 'outbound') NOT NULL,
  duration INT DEFAULT NULL, -- seconds
  notes TEXT,
  recording_url VARCHAR(255), -- file ID from file service
  start_time TIMESTAMP NOT NULL,
  end_time TIMESTAMP DEFAULT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_customer_phone (customer_phone),
  INDEX idx_admin_id (admin_id),
  INDEX idx_status (status),
  INDEX idx_direction (direction),
  INDEX idx_start_time (start_time),
  INDEX idx_recording_url (recording_url)
);
```

### 3.2 Bảng files (Đã có sẵn)
Sử dụng bảng files hiện tại để lưu trữ file ghi âm.

## 4. Frontend Implementation

### 4.1 Cập nhật sipService.ts

#### Thêm các thuộc tính mới:
```typescript
class SipService {
    private mediaRecorder: MediaRecorder | null = null;
    private recordedChunks: Blob[] = [];
    private currentCallId: string | null = null;
    private recordingStartTime: Date | null = null;
}
```

#### Cập nhật phương thức ghi âm:
```typescript
/**
 * Bắt đầu ghi âm cuộc gọi thực tế
 */
public async startRecording(): Promise<void> {
    if (this.callState.status !== CallStatus.CONNECTED) {
        throw new Error('Không thể bắt đầu ghi âm: Không có cuộc gọi đang hoạt động');
    }

    try {
        // Tạo MediaRecorder từ audio stream
        const audioStream = await this.getAudioStream();
        this.mediaRecorder = new MediaRecorder(audioStream, {
            mimeType: 'audio/webm;codecs=opus'
        });
        
        this.recordedChunks = [];
        this.recordingStartTime = new Date();
        
        // Xử lý data khi có
        this.mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
                this.recordedChunks.push(event.data);
            }
        };
        
        // Bắt đầu ghi âm
        this.mediaRecorder.start(1000); // Lưu chunk mỗi 1 giây
        
        this.updateCallState({ recording: true });
        console.log('Bắt đầu ghi âm cuộc gọi');
        
    } catch (error) {
        console.error('Lỗi khi bắt đầu ghi âm:', error);
        throw new Error('Không thể bắt đầu ghi âm: ' + error.message);
    }
}

/**
 * Dừng ghi âm và upload file
 */
public async stopRecording(): Promise<string | null> {
    if (!this.callState.recording || !this.mediaRecorder) {
        console.warn('Không có ghi âm đang hoạt động');
        return null;
    }

    return new Promise((resolve, reject) => {
        this.mediaRecorder!.onstop = async () => {
            try {
                // Tạo file blob
                const recordingBlob = new Blob(this.recordedChunks, { 
                    type: 'audio/webm' 
                });
                
                // Tạo tên file
                const timestamp = this.recordingStartTime?.getTime() || Date.now();
                const fileName = `call-recording-${timestamp}.webm`;
                
                // Upload file
                const file = new File([recordingBlob], fileName, {
                    type: 'audio/webm'
                });
                
                const uploadResult = await fileUploadService.uploadFile(file, {
                    onProgress: (progress) => {
                        console.log(`Upload progress: ${progress}%`);
                    }
                });
                
                console.log('Upload file ghi âm thành công:', uploadResult._id);
                resolve(uploadResult._id);
                
            } catch (error) {
                console.error('Lỗi upload file ghi âm:', error);
                reject(error);
            } finally {
                // Cleanup
                this.recordedChunks = [];
                this.mediaRecorder = null;
                this.recordingStartTime = null;
            }
        };
        
        this.mediaRecorder!.stop();
        this.updateCallState({ recording: false });
    });
}
```

### 4.2 Tích hợp với CallCenterService

#### Thêm phương thức quản lý cuộc gọi:
```typescript
class CallCenterService {
    /**
     * Bắt đầu cuộc gọi mới
     */
    async startCall(phoneNumber: string, direction: 'inbound' | 'outbound'): Promise<Call> {
        return api.post<Call>('/calls', {
            customerPhone: phoneNumber,
            direction,
            startTime: new Date().toISOString()
        });
    }

    /**
     * Kết thúc cuộc gọi và lưu file ghi âm
     */
    async endCall(callId: string, data: {
        duration?: number;
        notes?: string;
        recordingUrl?: string;
    }): Promise<Call> {
        return api.put<Call>(`/calls/${callId}`, {
            ...data,
            status: 'completed',
            endTime: new Date().toISOString()
        });
    }

    /**
     * Lấy thống kê cuộc gọi
     */
    async getCallStats(params: {
        startDate?: string;
        endDate?: string;
        adminId?: string;
    } = {}): Promise<any> {
        return api.get('/calls/stats', { params });
    }
}
```

### 4.3 Tạo trang lịch sử cuộc gọi

#### Tạo component CallHistoryPage:
```typescript
// src/pages/call-center/pages/CallHistoryPage.tsx
import React, { useState, useEffect } from 'react';
import { callCenterService, Call } from '@/services/CallCenterService';
import { fileUploadService } from '@/services/FileService';

const CallHistoryPage: React.FC = () => {
    const [calls, setCalls] = useState<Call[]>([]);
    const [loading, setLoading] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);

    // Load danh sách cuộc gọi
    const loadCalls = async (page: number = 1) => {
        setLoading(true);
        try {
            const result = await callCenterService.getCalls({
                page,
                limit: 20,
                sortBy: 'startTime',
                sortOrder: 'desc'
            });

            setCalls(result.data);
            setCurrentPage(result.page);
            setTotalPages(result.totalPages);
        } catch (error) {
            console.error('Lỗi khi tải lịch sử cuộc gọi:', error);
        } finally {
            setLoading(false);
        }
    };

    // Phát file ghi âm
    const playRecording = (call: Call) => {
        if (call.recordingUrl) {
            const audioUrl = fileUploadService.getFileContentUrl(call.recordingUrl);
            const audio = new Audio(audioUrl);
            audio.play();
        }
    };

    // Tải file ghi âm
    const downloadRecording = (call: Call) => {
        if (call.recordingUrl) {
            const downloadUrl = fileUploadService.getFileContentUrl(call.recordingUrl);
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = `ghi-am-${call.customerPhone}-${call.startTime}.webm`;
            link.click();
        }
    };

    useEffect(() => {
        loadCalls();
    }, []);

    return (
        <div className="space-y-6">
            <PageHeader
                title="Lịch sử cuộc gọi"
                description="Quản lý và xem lại các cuộc gọi đã thực hiện"
            />

            {/* Bảng danh sách cuộc gọi */}
            <DataTable
                data={calls}
                columns={[
                    {
                        header: 'Khách hàng',
                        cell: (call) => (
                            <div>
                                <div className="font-medium">{call.customerName || 'Không rõ'}</div>
                                <div className="text-sm text-gray-500">{call.customerPhone}</div>
                            </div>
                        )
                    },
                    {
                        header: 'Nhân viên',
                        cell: (call) => call.adminName || 'Không rõ'
                    },
                    {
                        header: 'Thời gian',
                        cell: (call) => formatDateTime(call.startTime)
                    },
                    {
                        header: 'Thời lượng',
                        cell: (call) => call.duration ? formatDuration(call.duration) : '-'
                    },
                    {
                        header: 'Trạng thái',
                        cell: (call) => <CallStatusBadge status={call.status} />
                    },
                    {
                        header: 'Ghi âm',
                        cell: (call) => call.recordingUrl ? (
                            <div className="flex gap-2">
                                <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => playRecording(call)}
                                >
                                    <Play className="h-4 w-4" />
                                </Button>
                                <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => downloadRecording(call)}
                                >
                                    <Download className="h-4 w-4" />
                                </Button>
                            </div>
                        ) : (
                            <span className="text-gray-400">Không có</span>
                        )
                    }
                ]}
                loading={loading}
            />

            {/* Pagination */}
            <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={loadCalls}
            />
        </div>
    );
};
```

## 5. Quy trình triển khai

### 5.1 Phase 1: Backend APIs
1. **Tạo migration database** cho bảng calls
2. **Triển khai API endpoints** cho quản lý cuộc gọi
3. **Cập nhật file upload service** để hỗ trợ audio files
4. **Test APIs** với Postman/Thunder Client

### 5.2 Phase 2: Frontend Core
1. **Cập nhật sipService.ts** với MediaRecorder
2. **Tích hợp với CallCenterService**
3. **Cập nhật CallInterface.tsx** để lưu cuộc gọi
4. **Test ghi âm cơ bản**

### 5.3 Phase 3: UI/UX
1. **Tạo CallHistoryPage**
2. **Thêm audio player component**
3. **Tích hợp với routing**
4. **Thêm filters và search**

### 5.4 Phase 4: Optimization
1. **Thêm compression cho audio files**
2. **Implement streaming playback**
3. **Thêm thống kê và reports**
4. **Performance optimization**

## 6. Cấu hình bổ sung

### 6.1 Environment Variables
```bash
# File upload limits
VITE_MAX_RECORDING_SIZE=50MB
VITE_MAX_RECORDING_DURATION=3600 # 1 hour in seconds

# Audio recording settings
VITE_AUDIO_CODEC=opus
VITE_AUDIO_BITRATE=64000
VITE_AUDIO_SAMPLE_RATE=48000

# Storage settings
VITE_RECORDING_STORAGE_PATH=/uploads/recordings/
VITE_RECORDING_RETENTION_DAYS=365
```

### 6.2 Audio Configuration
```typescript
// src/pages/call-center/config/audioConfig.ts
export const AUDIO_CONFIG = {
  // MediaRecorder options
  RECORDER_OPTIONS: {
    mimeType: 'audio/webm;codecs=opus',
    audioBitsPerSecond: 64000
  },

  // Fallback options for different browsers
  FALLBACK_MIME_TYPES: [
    'audio/webm;codecs=opus',
    'audio/webm',
    'audio/mp4',
    'audio/wav'
  ],

  // Recording constraints
  MAX_DURATION: 3600, // 1 hour
  CHUNK_INTERVAL: 1000, // 1 second

  // Audio player settings
  PLAYER_CONTROLS: true,
  PRELOAD: 'metadata'
};
```

## 7. Error Handling & Validation

### 7.1 Frontend Error Handling
```typescript
// src/pages/call-center/utils/recordingErrors.ts
export enum RecordingError {
  MEDIA_NOT_SUPPORTED = 'MEDIA_NOT_SUPPORTED',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  RECORDING_FAILED = 'RECORDING_FAILED',
  UPLOAD_FAILED = 'UPLOAD_FAILED',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE'
}

export const getErrorMessage = (error: RecordingError): string => {
  switch (error) {
    case RecordingError.MEDIA_NOT_SUPPORTED:
      return 'Trình duyệt không hỗ trợ ghi âm. Vui lòng sử dụng Chrome, Firefox hoặc Edge.';
    case RecordingError.PERMISSION_DENIED:
      return 'Quyền truy cập microphone bị từ chối. Vui lòng cấp quyền và thử lại.';
    case RecordingError.RECORDING_FAILED:
      return 'Ghi âm thất bại. Vui lòng kiểm tra microphone và thử lại.';
    case RecordingError.UPLOAD_FAILED:
      return 'Upload file ghi âm thất bại. Vui lòng kiểm tra kết nối mạng.';
    case RecordingError.FILE_TOO_LARGE:
      return 'File ghi âm quá lớn. Vui lòng ghi âm trong thời gian ngắn hơn.';
    default:
      return 'Có lỗi xảy ra khi ghi âm. Vui lòng thử lại.';
  }
};
```

### 7.2 Backend Validation
```javascript
// Backend validation schema (Node.js/Express example)
const callValidationSchema = {
  customerPhone: {
    type: 'string',
    required: true,
    pattern: /^[0-9+\-\s()]+$/,
    minLength: 10,
    maxLength: 15
  },
  direction: {
    type: 'string',
    required: true,
    enum: ['inbound', 'outbound']
  },
  duration: {
    type: 'number',
    min: 0,
    max: 3600
  },
  notes: {
    type: 'string',
    maxLength: 1000
  }
};

// File upload validation
const audioFileValidation = {
  allowedMimeTypes: [
    'audio/webm',
    'audio/mp4',
    'audio/wav',
    'audio/mpeg'
  ],
  maxFileSize: 50 * 1024 * 1024, // 50MB
  maxDuration: 3600 // 1 hour
};
```

## 8. Security Considerations

### 8.1 File Access Control
```typescript
// Backend middleware for recording access
const checkRecordingAccess = async (req, res, next) => {
  try {
    const { fileId } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    // Admin có thể truy cập tất cả recordings
    if (userRole === 'admin') {
      return next();
    }

    // Kiểm tra quyền truy cập file
    const call = await Call.findOne({ recordingUrl: fileId });
    if (!call) {
      return res.status(404).json({ error: 'Recording not found' });
    }

    // Chỉ admin tham gia cuộc gọi mới được truy cập
    if (call.adminId !== userId) {
      return res.status(403).json({ error: 'Access denied' });
    }

    next();
  } catch (error) {
    res.status(500).json({ error: 'Server error' });
  }
};
```

### 8.2 Data Privacy
```typescript
// Anonymization for recordings
const anonymizeRecording = (call: Call, userRole: string) => {
  if (userRole !== 'admin' && userRole !== 'manager') {
    return {
      ...call,
      customerName: 'Khách hàng',
      customerPhone: call.customerPhone.replace(/(\d{3})\d{4}(\d{3})/, '$1****$2'),
      notes: call.notes ? '[Đã ẩn]' : null
    };
  }
  return call;
};
```

## 9. Performance Optimization

### 9.1 Audio Compression
```typescript
// src/pages/call-center/utils/audioCompression.ts
export class AudioCompressor {
  static async compressAudio(audioBlob: Blob): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const audioContext = new AudioContext();
      const fileReader = new FileReader();

      fileReader.onload = async (e) => {
        try {
          const arrayBuffer = e.target?.result as ArrayBuffer;
          const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

          // Compress audio (reduce sample rate, channels)
          const compressedBuffer = await this.downsampleBuffer(audioBuffer, 16000);
          const compressedBlob = await this.bufferToBlob(compressedBuffer);

          resolve(compressedBlob);
        } catch (error) {
          reject(error);
        }
      };

      fileReader.readAsArrayBuffer(audioBlob);
    });
  }

  private static async downsampleBuffer(buffer: AudioBuffer, targetSampleRate: number): Promise<AudioBuffer> {
    // Implementation for downsampling
    // This is a simplified version - use a proper audio processing library in production
    const ratio = buffer.sampleRate / targetSampleRate;
    const newLength = Math.round(buffer.length / ratio);
    const newBuffer = new AudioContext().createBuffer(1, newLength, targetSampleRate);

    // Downsample logic here...
    return newBuffer;
  }
}
```

### 9.2 Lazy Loading & Pagination
```typescript
// src/pages/call-center/hooks/useCallHistory.ts
export const useCallHistory = () => {
  const [calls, setCalls] = useState<Call[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);

  const loadMore = useCallback(async () => {
    if (loading || !hasMore) return;

    setLoading(true);
    try {
      const result = await callCenterService.getCalls({
        page,
        limit: 20
      });

      setCalls(prev => [...prev, ...result.data]);
      setHasMore(result.page < result.totalPages);
      setPage(prev => prev + 1);
    } catch (error) {
      console.error('Error loading calls:', error);
    } finally {
      setLoading(false);
    }
  }, [page, loading, hasMore]);

  return { calls, loading, hasMore, loadMore };
};
```

## 10. Testing Strategy

### 10.1 Unit Tests
```typescript
// src/pages/call-center/services/__tests__/sipService.test.ts
describe('SipService Recording', () => {
  let sipService: SipService;

  beforeEach(() => {
    sipService = new SipService();
    // Mock MediaRecorder
    global.MediaRecorder = jest.fn().mockImplementation(() => ({
      start: jest.fn(),
      stop: jest.fn(),
      ondataavailable: null,
      onstop: null
    }));
  });

  test('should start recording when call is connected', async () => {
    // Setup connected call state
    sipService.updateCallState({ status: CallStatus.CONNECTED });

    await sipService.startRecording();

    expect(sipService.callState.recording).toBe(true);
  });

  test('should throw error when starting recording without active call', async () => {
    sipService.updateCallState({ status: CallStatus.IDLE });

    await expect(sipService.startRecording()).rejects.toThrow(
      'Không thể bắt đầu ghi âm: Không có cuộc gọi đang hoạt động'
    );
  });
});
```

### 10.2 Integration Tests
```typescript
// src/pages/call-center/__tests__/recording-integration.test.ts
describe('Recording Integration', () => {
  test('should complete full recording workflow', async () => {
    // 1. Start call
    const call = await callCenterService.startCall('0987654321', 'outbound');

    // 2. Start recording
    await sipService.startRecording();

    // 3. Simulate recording data
    // ... mock recording process

    // 4. Stop recording and upload
    const recordingUrl = await sipService.stopRecording();

    // 5. End call with recording
    await callCenterService.endCall(call.id, {
      duration: 120,
      recordingUrl
    });

    // 6. Verify call was saved with recording
    const savedCall = await callCenterService.getCallById(call.id);
    expect(savedCall.recordingUrl).toBe(recordingUrl);
  });
});
```

## 11. Monitoring & Analytics

### 11.1 Recording Metrics
```typescript
// src/pages/call-center/utils/recordingMetrics.ts
export class RecordingMetrics {
  static trackRecordingStart(callId: string) {
    // Analytics tracking
    analytics.track('recording_started', {
      callId,
      timestamp: new Date().toISOString()
    });
  }

  static trackRecordingEnd(callId: string, duration: number, fileSize: number) {
    analytics.track('recording_completed', {
      callId,
      duration,
      fileSize,
      timestamp: new Date().toISOString()
    });
  }

  static trackRecordingError(callId: string, error: string) {
    analytics.track('recording_error', {
      callId,
      error,
      timestamp: new Date().toISOString()
    });
  }
}
```

### 11.2 Performance Monitoring
```typescript
// Backend monitoring
const recordingMetrics = {
  totalRecordings: 0,
  totalStorageUsed: 0,
  averageFileSize: 0,
  recordingErrors: 0,

  updateMetrics(fileSize: number, success: boolean) {
    if (success) {
      this.totalRecordings++;
      this.totalStorageUsed += fileSize;
      this.averageFileSize = this.totalStorageUsed / this.totalRecordings;
    } else {
      this.recordingErrors++;
    }
  }
};
```

## 12. Deployment Checklist

### 12.1 Pre-deployment
- [ ] Database migration executed
- [ ] Environment variables configured
- [ ] File storage permissions set
- [ ] SSL certificates for WebRTC
- [ ] CORS settings updated
- [ ] Rate limiting configured

### 12.2 Post-deployment
- [ ] Test recording functionality
- [ ] Verify file upload/download
- [ ] Check audio playback
- [ ] Monitor error logs
- [ ] Validate storage usage
- [ ] Test different browsers

### 12.3 Rollback Plan
- [ ] Database rollback scripts ready
- [ ] Previous version deployment ready
- [ ] File cleanup procedures
- [ ] User notification plan

## 13. Documentation Updates

### 13.1 User Manual
```markdown
# Hướng dẫn sử dụng tính năng ghi âm

## Ghi âm cuộc gọi
1. Trong cuộc gọi, nhấn nút "Ghi âm"
2. Biểu tượng ghi âm sẽ chuyển sang màu đỏ
3. Để dừng ghi âm, nhấn "Dừng ghi âm"
4. File sẽ tự động được lưu khi kết thúc cuộc gọi

## Xem lịch sử cuộc gọi
1. Vào menu "Lịch sử cuộc gọi"
2. Tìm cuộc gọi có biểu tượng ghi âm
3. Nhấn nút "Phát" để nghe lại
4. Nhấn nút "Tải về" để download file

## Lưu ý
- Chỉ ghi âm khi có sự đồng ý của khách hàng
- File ghi âm được lưu trữ bảo mật
- Chỉ nhân viên liên quan mới có thể truy cập
```

### 13.2 API Documentation
```yaml
# OpenAPI specification for recording APIs
openapi: 3.0.0
info:
  title: Call Center Recording API
  version: 1.0.0

paths:
  /api/calls:
    get:
      summary: Get call history
      parameters:
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
        - name: hasRecording
          in: query
          schema:
            type: boolean
      responses:
        200:
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CallListResponse'
```

## 14. Maintenance & Support

### 14.1 Regular Maintenance Tasks
```bash
# Weekly cleanup script
#!/bin/bash
# cleanup-old-recordings.sh

# Delete recordings older than 1 year
find /uploads/recordings -name "*.webm" -mtime +365 -delete

# Update database to remove orphaned records
mysql -u user -p database << EOF
DELETE FROM calls
WHERE recording_url IS NOT NULL
AND recording_url NOT IN (
  SELECT _id FROM files WHERE fileType = 'audio'
);
EOF

# Generate storage usage report
du -sh /uploads/recordings/ > /var/log/recording-storage.log
```

### 14.2 Troubleshooting Guide
```markdown
# Troubleshooting Recording Issues

## Ghi âm không hoạt động
1. Kiểm tra quyền microphone trong browser
2. Thử refresh trang và cấp quyền lại
3. Kiểm tra console log để xem lỗi cụ thể

## File upload thất bại
1. Kiểm tra kết nối mạng
2. Xem file size có vượt quá giới hạn không
3. Kiểm tra disk space trên server

## Không thể phát file ghi âm
1. Kiểm tra file có tồn tại trên server không
2. Thử download file để test
3. Kiểm tra CORS settings
```

## 15. Kết luận

Tài liệu này cung cấp hướng dẫn đầy đủ để triển khai tính năng ghi âm cuộc gọi trong hệ thống call center. Việc triển khai cần được thực hiện theo từng phase để đảm bảo tính ổn định và chất lượng.

### Ưu tiên triển khai:
1. **Phase 1**: Backend APIs và Database
2. **Phase 2**: Frontend Core Recording
3. **Phase 3**: UI/UX và Call History
4. **Phase 4**: Optimization và Advanced Features

### Lưu ý quan trọng:
- Đảm bảo tuân thủ quy định về bảo mật dữ liệu
- Test kỹ lưỡng trên nhiều trình duyệt
- Có kế hoạch backup và recovery
- Monitor performance và storage usage

Bạn có thể bắt đầu triển khai từ Phase 1 và tiến hành từng bước theo tài liệu này.
