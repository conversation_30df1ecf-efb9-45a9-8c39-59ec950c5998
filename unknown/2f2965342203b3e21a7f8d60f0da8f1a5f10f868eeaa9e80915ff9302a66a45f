import { useState, useEffect } from "react";
import { CustomerLayout } from "@/components/layout/CustomerLayout";
import { SEO } from "@/components/SEO";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/contexts/AuthContext";
import {
  History,
  Package,
  Search,
  Eye,
  Calendar,
  Filter,
  Truck,
  CheckCircle,
  Clock,
  XCircle
} from "lucide-react";

interface Order {
  id: string;
  orderNumber: string;
  date: string;
  status: 'pending' | 'processing' | 'shipping' | 'delivered' | 'cancelled';
  total: string;
  items: number;
  trackingNumber?: string;
}

interface SupportTicket {
  id: string;
  title: string;
  type: 'order' | 'payment' | 'shipping' | 'other';
  status: 'open' | 'in-progress' | 'resolved' | 'closed';
  date: string;
  lastUpdate: string;
}

export default function CustomerHistory() {
  const { user } = useAuth();
  const [orders, setOrders] = useState<Order[]>([]);
  const [supportTickets, setSupportTickets] = useState<SupportTicket[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("orders");

  const userRole = user?.role || 'shop';
  const userTitle = userRole === 'buyer' ? 'Người mua' : 'Cửa hàng';

  useEffect(() => {
    const fetchHistoryData = async () => {
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        const mockOrders: Order[] = [
          {
            id: "1",
            orderNumber: "DH001234",
            date: "2024-01-15",
            status: "delivered",
            total: "1.250.000đ",
            items: 3,
            trackingNumber: "VN123456789"
          },
          {
            id: "2",
            orderNumber: "DH001235",
            date: "2024-01-20",
            status: "shipping",
            total: "850.000đ",
            items: 2,
            trackingNumber: "VN123456790"
          },
          {
            id: "3",
            orderNumber: "DH001236",
            date: "2024-01-22",
            status: "processing",
            total: "2.100.000đ",
            items: 5
          },
          {
            id: "4",
            orderNumber: "DH001237",
            date: "2024-01-25",
            status: "pending",
            total: "650.000đ",
            items: 1
          }
        ];

        const mockTickets: SupportTicket[] = [
          {
            id: "1",
            title: "Hỗ trợ đơn hàng #DH001234",
            type: "order",
            status: "resolved",
            date: "2024-01-16",
            lastUpdate: "2024-01-17"
          },
          {
            id: "2",
            title: "Thắc mắc về phí vận chuyển",
            type: "shipping",
            status: "closed",
            date: "2024-01-18",
            lastUpdate: "2024-01-19"
          },
          {
            id: "3",
            title: "Vấn đề thanh toán COD",
            type: "payment",
            status: "in-progress",
            date: "2024-01-24",
            lastUpdate: "2024-01-25"
          }
        ];

        setOrders(mockOrders);
        setSupportTickets(mockTickets);
      } catch (error) {
        console.error("Error fetching history data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchHistoryData();
  }, []);

  const getOrderStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: 'Chờ xử lý', variant: 'secondary' as const, color: 'text-yellow-600 bg-yellow-50' },
      processing: { label: 'Đang xử lý', variant: 'secondary' as const, color: 'text-blue-600 bg-blue-50' },
      shipping: { label: 'Đang giao', variant: 'secondary' as const, color: 'text-purple-600 bg-purple-50' },
      delivered: { label: 'Đã giao', variant: 'secondary' as const, color: 'text-green-600 bg-green-50' },
      cancelled: { label: 'Đã hủy', variant: 'secondary' as const, color: 'text-red-600 bg-red-50' }
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return (
      <Badge variant={config.variant} className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const getOrderStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'processing':
        return <Package className="h-4 w-4 text-blue-600" />;
      case 'shipping':
        return <Truck className="h-4 w-4 text-purple-600" />;
      case 'delivered':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  const getSupportStatusBadge = (status: string) => {
    const statusConfig = {
      open: { label: 'Mở', variant: 'secondary' as const, color: 'text-blue-600 bg-blue-50' },
      'in-progress': { label: 'Đang xử lý', variant: 'secondary' as const, color: 'text-yellow-600 bg-yellow-50' },
      resolved: { label: 'Đã giải quyết', variant: 'secondary' as const, color: 'text-green-600 bg-green-50' },
      closed: { label: 'Đã đóng', variant: 'secondary' as const, color: 'text-gray-600 bg-gray-50' }
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return (
      <Badge variant={config.variant} className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const getSupportTypeLabel = (type: string) => {
    const typeLabels = {
      order: 'Đơn hàng',
      payment: 'Thanh toán',
      shipping: 'Vận chuyển',
      other: 'Khác'
    };
    return typeLabels[type as keyof typeof typeLabels] || 'Khác';
  };

  const filteredOrders = orders.filter(order =>
    order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.total.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredTickets = supportTickets.filter(ticket =>
    ticket.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    getSupportTypeLabel(ticket.type).toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <CustomerLayout>
      <SEO 
        title={`Lịch sử đơn hàng - ${userTitle}`} 
        description="Xem lịch sử đơn hàng và yêu cầu hỗ trợ" 
      />
      
      <div className="space-y-6">
        {/* Page Header */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-2">
            Lịch sử đơn hàng
          </h1>
          <p className="text-muted-foreground">
            Theo dõi đơn hàng và yêu cầu hỗ trợ của bạn
          </p>
        </div>

        {/* Search */}
        <Card>
          <CardContent className="pt-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Tìm kiếm theo mã đơn hàng, số tiền..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="orders" className="flex items-center gap-2">
              <Package className="h-4 w-4" />
              Đơn hàng ({orders.length})
            </TabsTrigger>
            <TabsTrigger value="support" className="flex items-center gap-2">
              <History className="h-4 w-4" />
              Hỗ trợ ({supportTickets.length})
            </TabsTrigger>
          </TabsList>

          {/* Orders Tab */}
          <TabsContent value="orders" className="space-y-4">
            {loading ? (
              <Card>
                <CardContent className="text-center py-8">
                  <div className="text-muted-foreground">Đang tải...</div>
                </CardContent>
              </Card>
            ) : filteredOrders.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <Package className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                  <p className="text-muted-foreground">
                    {searchTerm ? 'Không tìm thấy đơn hàng nào' : 'Chưa có đơn hàng nào'}
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {filteredOrders.map((order) => (
                  <Card key={order.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-3">
                          {getOrderStatusIcon(order.status)}
                          <div>
                            <h3 className="font-semibold">#{order.orderNumber}</h3>
                            <p className="text-sm text-muted-foreground">
                              {new Date(order.date).toLocaleDateString('vi-VN')}
                            </p>
                          </div>
                        </div>
                        {getOrderStatusBadge(order.status)}
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                        <div>
                          <p className="text-xs text-muted-foreground">Tổng tiền</p>
                          <p className="font-semibold">{order.total}</p>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground">Số sản phẩm</p>
                          <p className="font-semibold">{order.items} sản phẩm</p>
                        </div>
                        {order.trackingNumber && (
                          <div className="md:col-span-2">
                            <p className="text-xs text-muted-foreground">Mã vận đơn</p>
                            <p className="font-semibold text-primary">{order.trackingNumber}</p>
                          </div>
                        )}
                      </div>

                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          Xem chi tiết
                        </Button>
                        {order.trackingNumber && (
                          <Button variant="outline" size="sm">
                            <Truck className="h-4 w-4 mr-1" />
                            Theo dõi
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          {/* Support Tab */}
          <TabsContent value="support" className="space-y-4">
            {loading ? (
              <Card>
                <CardContent className="text-center py-8">
                  <div className="text-muted-foreground">Đang tải...</div>
                </CardContent>
              </Card>
            ) : filteredTickets.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <History className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                  <p className="text-muted-foreground">
                    {searchTerm ? 'Không tìm thấy yêu cầu hỗ trợ nào' : 'Chưa có yêu cầu hỗ trợ nào'}
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {filteredTickets.map((ticket) => (
                  <Card key={ticket.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h3 className="font-semibold mb-1">{ticket.title}</h3>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Badge variant="outline" className="text-xs">
                              {getSupportTypeLabel(ticket.type)}
                            </Badge>
                            <span>•</span>
                            <span>{new Date(ticket.date).toLocaleDateString('vi-VN')}</span>
                          </div>
                        </div>
                        {getSupportStatusBadge(ticket.status)}
                      </div>

                      <div className="mb-4">
                        <p className="text-sm text-muted-foreground">
                          Cập nhật lần cuối: {new Date(ticket.lastUpdate).toLocaleDateString('vi-VN')}
                        </p>
                      </div>

                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-1" />
                        Xem chi tiết
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </CustomerLayout>
  );
}
