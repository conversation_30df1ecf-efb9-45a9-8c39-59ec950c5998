import {cskh_api} from "@/lib/cskh.api.ts";

export interface Conversation {
  "_id": string,
  "title": string,
  status: 'chatbot' | 'waiting' | 'open' | 'close' | 'archived',
  "userId": string,
  "user": unknown,
  "staffId": string,
  "staff"?: unknown,
  "topicId": string,
  "topic": unknown,
  "ownerUserId": string,
  "lastMessage": string,
  "isStarred": boolean,
  "meta": unknown,
  "feedback": {
    rating: number,
    comment: string,
  } | null,
  "createdAt": string,
  "updatedAt": string,

  "unread": number,
}

export interface Message {
  "_id": string,
  "text": string,
  "refId": string,
  "senderId": string,
  "roomId": string,
  "images": string[],
  "file": string,
  "location": null,
  "read": boolean,
  "isZip": boolean,
  "status": string,
  "senderType": 'user' | 'bot' | 'staff',
  "createdAt": string,
  "updatedAt": string
}

export interface Sender {
  "_id": string,
  "fullName": string,
  "email": string,
  "gender": string,
  "phone": string,
  "avatar": string,
  "isSystemAdmin": boolean,
  "role": 'shop' | 'cskh' | 'admin' | 'buyer'
}

interface updateConversation {
  feedback?: {
    rating: number,
    comment?: string,
  }
  meta?: unknown
  isStarred?: boolean
}


interface updateStatus {
  status: 'chatbot' | 'open' | 'close',
}



interface paramsConversations {
  pagination?: boolean;
  page?: number;
  limit?: number;
  userId: string;
  title?: string;
}

interface paramsUserConversations {
  pagination?: boolean;
  page?: number;
  limit?: number;
  title?: string;
}

interface paramsMessages {
  pagination?: boolean;
  page?: number;
  limit?: number;
}

export interface bodyMessages {
  text?: string;
  images?: string[];
  file?: string;
}

export interface CreateConversation {
  topicId: string,
  userId: string,
  staffId?: string,
}


const API = {
  MESSAGES: '/messages',
  SEND_MESSAGE: '/messages/:conversationId/send',
  READ_MESSAGE: '/conversations/:conversationId/read',
  CONVERSATIONS: '/conversations',
  STAFF_CONVERSATION: '/conversations/:conversationId/staff',
  CONVERSATIONS_ID: '/conversations/:conversationId',
  UPDATE_STATUS: '/conversations/:conversationId/status',
  GET_CUSTOMER_CONVERSATION: '/conversations/customer',
};


class CskhService {

  async createConversations(body: CreateConversation): Promise<unknown> {
    return cskh_api.post(API.CONVERSATIONS, body);
  }

  async getConversations(params: paramsConversations): Promise<unknown> {
    return cskh_api.get(API.CONVERSATIONS, params);
  }

  async markReadMessage(conversationId): Promise<unknown> {
    const url = API.READ_MESSAGE.replace(':conversationId', conversationId);
    return cskh_api.get(url);
  }

  async updateConversation(conversationId, body: updateConversation): Promise<unknown> {
    const url = API.CONVERSATIONS_ID.replace(':conversationId', conversationId);
    return cskh_api.put(url, body);
  }

  async updateStatus(conversationId, body: updateStatus): Promise<unknown> {
    const url = API.UPDATE_STATUS.replace(':conversationId', conversationId);
    return cskh_api.put(url, body);
  }

  async staffRequirements(conversationId): Promise<unknown> {
    const url = API.STAFF_CONVERSATION.replace(':conversationId', conversationId);
    return cskh_api.get(url);
  }

  async getMessages(conversationId: string, params: paramsMessages): Promise<unknown> {
    return cskh_api.get(`${API.MESSAGES}/${conversationId}`, params);
  }

  async sendMessages(conversationId: string, body: bodyMessages): Promise<unknown> {
    const url = API.SEND_MESSAGE.replace(':conversationId', conversationId);
    return cskh_api.post(url, body);
  }

  async getCustomerConversations(params: paramsUserConversations): Promise<unknown> {
    return cskh_api.get(API.GET_CUSTOMER_CONVERSATION, params);
  }
}

export const cskhService = new CskhService();