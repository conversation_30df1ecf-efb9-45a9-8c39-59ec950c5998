import {cskh_api} from "@/lib/cskh.api.ts";

export interface Conversation {
    "_id": string,
    "title": string,
    "status": string,
    "userIds": string[],
    "blockUserIds": string[],
    "ownerUserId": string,
    "lastMessage": string,
    "meta": unknown,
    "createdAt": string,
    "updatedAt": string,
    "users": unknown,
    "unread": number,
}

export interface Message {
    "_id": string,
    "text": string,
    "refId": string,
    "senderId": string,
    "roomId": string,
    "images": string[],
    "file": string,
    "location": null,
    "read": boolean,
    "isZip": boolean,
    "status": string,
    "createdAt": string,
    "updatedAt": string
}

export interface Sender {
    "_id": string,
    "fullName": string,
    "email": string,
    "gender": string,
    "phone": string,
    "avatar": string,
    "isSystemAdmin": boolean,
    "role": 'shop' | 'cskh' | 'admin' | 'buyer'
}

interface updateConversation {
    title?: string,
    status?: 'chatbot' | 'waiting' | 'open' | 'close' | 'archived',
    user_ids?: string[],
    block_user_ids?: string[],
    meta?: unknown
}

interface paramsConversations {
    pagination?: boolean;
    page?: number;
    limit?: number;
    userId: string;
    title?: string;
}

interface paramsMessages {
    pagination?: boolean;
    page?: number;
    limit?: number;
}

export interface bodyMessages {
    text?: string;
    images?: string[];
    file?: string;
}



const API = {
    VERSION: '/version',
    CONVERSATIONS: '/conversations',
    MESSAGES: '/messages',
    SEND_MESSAGE: '/messages/:conversationId/send',
    READ_MESSAGE: '/conversations/:conversationId/read',
    CONVERSATIONS_ID: '/conversations/:conversationId',
    GET_CUSTOMER_CONVERSATION: '/conversations/customer',
};


class CskhService {

    async createConversations(): Promise<unknown> {
        return cskh_api.post(API.CONVERSATIONS);
    }

    async getVersion(): Promise<unknown> {
        return cskh_api.get(API.VERSION);
    }

    async getConversations(params: paramsConversations): Promise<unknown> {
        return cskh_api.get(API.CONVERSATIONS, params);
    }

    async markReadMessage(conversationId): Promise<unknown> {
        const url = API.READ_MESSAGE.replace(':conversationId', conversationId);
        return cskh_api.get(url);
    }

    async updateConversation(conversationId, body: updateConversation): Promise<unknown> {
        const url = API.CONVERSATIONS_ID.replace(':conversationId', conversationId);
        return cskh_api.put(url, body);
    }

    async getMessages(conversationId: string, params: paramsMessages): Promise<unknown> {
        return cskh_api.get(`${API.MESSAGES}/${conversationId}`, params);
    }

    async sendMessages(conversationId: string, body: bodyMessages): Promise<unknown> {
        const url = API.SEND_MESSAGE.replace(':conversationId', conversationId);
        return cskh_api.post(url, body);
    }

    async getCustomerConversations(): Promise<unknown> {
        return cskh_api.get(API.GET_CUSTOMER_CONVERSATION);
    }
}

export const cskhService = new CskhService();