import {cskh_api, ListResponse} from "@/lib/cskh.api.ts";

export interface Knowledge {
  _id: string;
  filename: string;
  filepath: string;
  previewUrl: string;
  contentType: string;
  status: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}


export interface KnowledgeParams {
  pagination: boolean;
  page: number;
  limit: number
  filename?: string;
  isActive?: boolean;
}


const API = {
  KNOWLEDGE: '/knowledges',
  KNOWLEDGE_ID: '/knowledges/:knowledgesId',
  KNOWLEDGE_CHANGE_ACTIVE: '/knowledges/:knowledgesId/change-active',
};

class KnowledgeService {

  async getAllKnowledge(params: KnowledgeParams): Promise<ListResponse> {
    return cskh_api.get(API.KNOWLEDGE, params);
  }

  async createKnowledge(formData: FormData): Promise<{ success: boolean, data: Knowledge[] }> {
    return cskh_api.post(API.KNOWLEDGE, formData);
  }

  async changeActiveKnowledge(id, body): Promise<{ success: boolean, data: Knowledge }> {
    const url = API.KNOWLEDGE_CHANGE_ACTIVE.replace(':knowledgesId', id);
    return cskh_api.put(url, body);
  }

  async deleteKnowledge(id): Promise<{ success: boolean }> {
    const url = API.KNOWLEDGE_ID.replace(':knowledgesId', id);
    return cskh_api.delete(url);
  }


}

export const knowledgeService = new KnowledgeService();