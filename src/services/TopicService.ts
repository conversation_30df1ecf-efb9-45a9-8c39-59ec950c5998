import {cskh_api, ListResponse} from "@/lib/cskh.api.ts";

export interface Topic {
    _id: string;
    title: string
    prompt: string
    description: string
    isActive: boolean;
    isDeleted: boolean;
    createdAt: string;
    updatedAt: string;
}


export interface TopicParams {
    pagination: boolean;
    page?: number;
    limit?: number
    title?: string;
}

export interface TopicBody {
    title: string
    prompt?: string
    description?: string
}


const API = {
    TOPIC: '/topics',
    TOPIC_ID: '/topics/:topicId',
};

class TopicService {

    async getAllTopic(params: TopicParams): Promise<ListResponse> {
        return cskh_api.get(API.TOPIC, params);
    }

    async getTopic(id: string): Promise<Topic> {
        const url = API.TOPIC_ID.replace(':topicId', id);
        return cskh_api.get(url);
    }

    async createTopic(body: TopicBody): Promise<{ success: boolean, data: Topic[] }> {
        return cskh_api.post(API.TOPIC, body);
    }

    async updateTopic(id: string, body: TopicBody): Promise<{ success: boolean, data: Topic }> {
        const url = API.TOPIC_ID.replace(':topicId', id);
        return cskh_api.put(url, body);
    }

    async deleteTopic(id: string): Promise<{ success: boolean }> {
        const url = API.TOPIC_ID.replace(':topicId', id);
        return cskh_api.delete(url);
    }


}

export const topicService = new TopicService();