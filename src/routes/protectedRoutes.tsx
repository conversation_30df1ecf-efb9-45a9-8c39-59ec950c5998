import { Route, Navigate } from "react-router-dom";
import { PrivateRoute } from "@/components/auth/PrivateRoute";
import { RoleBasedRoute } from "@/components/auth/RoleBasedRoute";

// Dashboard Pages
import Dashboard from "@/pages/dashboard";
import Call<PERSON>enter from "@/pages/call-center";
import Admins from "@/pages/admins";
import Chat from "@/pages/chat";
import CustomerCare from "@/pages/customer-care/staff";
import ProfilePage from "@/pages/profile";
import ConfigOpenai from "@/pages/config-openai";
import Knowledge from "@/pages/knowledge";

export const ProtectedRoutes = (
  <Route element={<PrivateRoute />}>
    <Route element={<RoleBasedRoute allowedRoles={["admin", "cskh"]} redirectTo="/dashboard" />}>
      <Route path="/dashboard" element={<Dashboard />} />
      <Route path="/call-center" element={<CallCenter />} />
      <Route path="/admins" element={<Admins />} />
      {/*<Route path="/customers" element={<Customers />} />*/}
      <Route path="/chat" element={<Chat />} />
      <Route path="/customers-care" element={<CustomerCare />} />
      <Route path="/config-openai" element={<ConfigOpenai />} />
      <Route path="/knowledge" element={<Knowledge />} />
    </Route>
    <Route path="/profile" element={<ProfilePage />} />
  </Route>
);
