import { useState, useCallback } from 'react';

export interface Document {
  id: string;
  name: string;
  uploadDate: string;
  status: boolean;
  file?: File;
}

export const useDocuments = () => {
  const [documents, setDocuments] = useState<Document[]>([
    {
      id: '1',
      name: '<PERSON><PERSON><PERSON> cáo tháng 1.pdf',
      uploadDate: '2024-01-15',
      status: true,
    },
    {
      id: '2',
      name: '<PERSON><PERSON><PERSON> đồng ABC.docx',
      uploadDate: '2024-01-14',
      status: false,
    },
    {
      id: '3',
      name: 'Thông báo nội bộ.pdf',
      uploadDate: '2024-01-13',
      status: true,
    },
    {
      id: '4',
      name: '<PERSON>uy định mới.docx',
      uploadDate: '2024-01-12',
      status: true,
    },
    {
      id: '5',
      name: '<PERSON><PERSON><PERSON> c<PERSON><PERSON> tài chính.xlsx',
      uploadDate: '2024-01-11',
      status: false,
    },
    {
      id: '6',
      name: '<PERSON><PERSON> hoạch Q1.pptx',
      uploadDate: '2024-01-10',
      status: true,
    },
    {
      id: '7',
      name: '<PERSON><PERSON><PERSON><PERSON> dẫn sử dụng.pdf',
      uploadDate: '2024-01-09',
      status: true,
    },
    {
      id: '8',
      name: '<PERSON>h sách nhân viên.xlsx',
      uploadDate: '2024-01-08',
      status: false,
    },
    {
      id: '9',
      name: 'Biên bản họp.docx',
      uploadDate: '2024-01-07',
      status: true,
    },
    {
      id: '10',
      name: 'Chính sách mới.pdf',
      uploadDate: '2024-01-06',
      status: true,
    },
    {
      id: '11',
      name: 'Báo cáo dự án.pdf',
      uploadDate: '2024-01-05',
      status: false,
    },
    {
      id: '12',
      name: 'Thống kê bán hàng.xlsx',
      uploadDate: '2024-01-04',
      status: true,
    },
  ]);

  const addDocument = useCallback((file: File) => {
    const newDocument: Document = {
      id: Date.now().toString(),
      name: file.name,
      uploadDate: new Date().toISOString().split('T')[0],
      status: true,
      file: file,
    };
    setDocuments(prev => [newDocument, ...prev]);
  }, []);

  const addMultipleDocuments = useCallback((files: File[]) => {

    const newDocuments: Document[] = files.map((file, index) => ({
      id: (Date.now() + index).toString(),
      name: file.name,
      uploadDate: new Date().toISOString().split('T')[0],
      status: true,
      file: file,
    }));
    setDocuments(prev => [...newDocuments, ...prev]);
  }, []);

  const deleteDocument = useCallback((id: string) => {
    setDocuments(prev => prev.filter(doc => doc.id !== id));
  }, []);

  const toggleStatus = useCallback((id: string) => {
    setDocuments(prev =>
      prev.map(doc =>
        doc.id === id ? { ...doc, status: !doc.status } : doc
      )
    );
  }, []);

  return {
    documents,
    addDocument,
    addMultipleDocuments,
    deleteDocument,
    toggleStatus,
  };
};