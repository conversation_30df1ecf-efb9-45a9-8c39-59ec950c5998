import { DefaultLayout } from "@/components/layout/DefaultLayout";
import { SEO } from "@/components/SEO";
import { APP_CONFIG } from "@/config/env";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Phone,
  MessageSquare,
  Users,
  Shield,
  Headphones,
  Clock,
  CheckCircle,
  Star
} from "lucide-react";

export default function Dashboard() {
  const features = [
    {
      icon: <Phone className="h-6 w-6" />,
      title: "Quản lý cuộc gọi",
      description: "<PERSON><PERSON> thống call center tích hợp với khả năng ghi âm và theo dõi cuộc gọi"
    },
    {
      icon: <MessageSquare className="h-6 w-6" />,
      title: "Chat trực tuyến",
      description: "Tương tác với khách hàng qua chat real-time, hỗ trợ đa kênh"
    },
    // {
    //   icon: <Users className="h-6 w-6" />,
    //   title: "Quản lý khách hàng",
    //   description: "Lưu trữ và quản lý thông tin khách hàng một cách hiệu quả"
    // },
    // {
    //   icon: <Shield className="h-6 w-6" />,
    //   title: "Bảo mật cao",
    //   description: "Đảm bảo an toàn thông tin với các tiêu chuẩn bảo mật hiện đại"
    // },
    {
      icon: <Headphones className="h-6 w-6" />,
      title: "Hỗ trợ 24/7",
      description: "Dịch vụ chăm sóc khách hàng liên tục, không gián đoạn"
    },
    // {
    //   icon: <Clock className="h-6 w-6" />,
    //   title: "Theo dõi thời gian thực",
    //   description: "Giám sát hoạt động và hiệu suất làm việc theo thời gian thực"
    // }
  ];

  return (
    <DefaultLayout>
      <SEO title="Trang chủ" description="Hệ thống quản trị chăm sóc khách hàng GHVN" />

      <div className="space-y-8">
        {/* Header Section with Logo */}
        <div className="text-center space-y-6">
          <div className="flex justify-center">
            <div className="app-logo w-32 h-32 p-4 bg-gradient-to-br from-primary/10 to-primary/5 rounded-2xl shadow-lg">
              <img
                src={APP_CONFIG.LOGO}
                alt="GHVN Logo"
                className="w-full h-full object-contain"
              />
            </div>
          </div>

          <div className="space-y-4">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              {APP_CONFIG.NAME}
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Hệ thống quản trị chăm sóc khách hàng toàn diện, hiện đại và hiệu quả
            </p>
            <div className="flex justify-center gap-2">
              <Badge variant="secondary" className="px-3 py-1">
                <Star className="h-3 w-3 mr-1" />
                Phiên bản mới nhất v1.0.0
              </Badge>
              {/*<Badge variant="outline" className="px-3 py-1">*/}
              {/*  <CheckCircle className="h-3 w-3 mr-1" />*/}
              {/*  Đã được kiểm thử*/}
              {/*</Badge>*/}
            </div>
          </div>
        </div>

        <Separator className="my-8" />

        {/* Features Section */}
        <div className="space-y-6">
          <div className="text-center">
            <h2 className="text-2xl font-semibold mb-2">Tính năng nổi bật</h2>
            <p className="text-muted-foreground">
              Khám phá các tính năng mạnh mẽ của hệ thống
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {features.map((feature, index) => (
              <Card key={index} className="group hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/20">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-primary/10 text-primary group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                        {feature.icon}
                      </div>
                      <h3 className="font-semibold text-lg">{feature.title}</h3>
                    </div>
                    <p className="text-muted-foreground leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Welcome Message */}
        <Card className="bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20">
          <CardContent className="p-8 text-center">
            <h3 className="text-xl font-semibold mb-3">Chào mừng bạn đến với hệ thống!</h3>
            <p className="text-muted-foreground mb-4">
              Sử dụng menu bên trái để điều hướng đến các chức năng khác nhau của hệ thống.
              Bạn có thể quản lý cuộc gọi, chat với khách hàng, và theo dõi các hoạt động một cách dễ dàng.
            </p>
            <div className="flex justify-center">
              <Badge variant="default" className="px-4 py-2">
                Bắt đầu sử dụng ngay
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>
    </DefaultLayout>
  );
}
