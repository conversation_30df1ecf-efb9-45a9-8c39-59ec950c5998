import React, {useRef, useEffect} from "react";
import {ScrollArea} from "@/components/ui/scroll-area.tsx";
import {useAuth} from "@/contexts/AuthContext.tsx";
import {Message, Conversation} from "@/services/CskhService.ts";
import {formatDate} from '@/lib/utils';
import MessageGroup from './MessageGroup.tsx'
import TopicChat from "@/pages/customer-care/customer/TopicChat";
import Loading from "@/components/custom/Loading";


interface ChatContentProps {
  messages: Message[]
  showAvatar?: boolean
  messagesLoading?: boolean
  showNewConvo?: boolean
  conversation?: Conversation
}

const ChatContent: React.FC<ChatContentProps> = ({
                                                   messages,
                                                   showAvatar = false,
                                                   messagesLoading = false,
                                                   showNewConvo = false,
                                                   conversation
                                                 }) => {
  const {user} = useAuth();
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const isFirstRender = useRef(true);
  const isUser = (user.role === "shop" || user.role === "buyer")
  useEffect(() => {
    isFirstRender.current = true;
  }, [conversation?._id]);

  useEffect(() => {
    const viewport = scrollAreaRef.current;
    if (!viewport) return;

    const scrollToBottom = () => {
      viewport.scrollTo({
        top: viewport.scrollHeight,
        behavior: isFirstRender.current ? "auto" : "smooth", // ✨
      });
    };

    scrollToBottom();
    isFirstRender.current = false;
  }, [messages]);


  const groupMessagesByDate = (messages: Message[]) => {
    const unread = messages.filter(msg => !msg.read && msg.senderId !== user._id);
    const read = messages.filter(msg => msg.read || msg.senderId === user._id);
    const groups: { [key: string]: Message[] } = {};

    read.forEach(message => {
      const dateKey = new Date(message.createdAt).toDateString();
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(message);
    });

    const result = Object.entries(groups).map(([dateKey, messages]) => ({
      groupName: formatDate(dateKey),
      messages
    }));

    if (unread.length > 0)
      result.push({groupName: 'Tin nhắn mới', messages: unread})

    return result
  };

  const messageGroups = groupMessagesByDate(messages);


  if (showNewConvo && isUser) return (
    <div
      className="flex-1 flex flex-col items-center justify-center bg-gradient-to-r from-primary/5 to-primary/10 min-h-0">
      <div className="flex-1 w-full overflow-y-auto min-h-0">
        <TopicChat/>
      </div>
    </div>
  )

  if (conversation && messages.length === 0 && isUser) return (
    <div
      className="flex-1 flex flex-col items-center justify-center bg-gradient-to-r from-primary/5 to-primary/10 min-h-0">
      <div className="text-muted-foreground text-xl">
        Gửi một tin nhắn để bắt đầu
      </div>
    </div>
  )

  if (messagesLoading) return (<div className="flex-1">
      <Loading size="lg" text="Đang tải dữ liệu..."/>
    </div>

  )

  return (
    <ScrollArea className="flex-1 bg-transparent p-4 " ref={scrollAreaRef}>
      <div
        className="space-y-4"
      >
        {messageGroups.map(({groupName, messages}, index) => (
          <MessageGroup
            key={index}
            showAvatar={showAvatar}
            messages={messages}
            user={user}
            groupName={groupName}
            conversation={conversation}
          />
        ))}
        <div ref={messagesEndRef}/>
      </div>
    </ScrollArea>
  )

}

export default ChatContent;