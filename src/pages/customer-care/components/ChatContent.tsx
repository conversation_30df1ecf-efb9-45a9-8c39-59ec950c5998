import React, {useRef, useEffect} from "react";
import {ScrollArea} from "@/components/ui/scroll-area.tsx";
import {useAuth} from "@/contexts/AuthContext.tsx";
import {Message, Conversation} from "@/services/CskhService.ts";
import {formatDate} from '@/lib/utils';
import MessageGroup from './MessageGroup.tsx'
import {Card, CardContent} from "@/components/ui/card.tsx";


interface ChatContentProps {
  messages: Message[]
  showAvatar?: boolean
  conversation?: Conversation
}

const ChatContent: React.FC<ChatContentProps> = ({
                                                   messages,
                                                   showAvatar = false,
                                                   conversation
                                                 }) => {
  const {user} = useAuth();
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const isFirstRender = useRef(true);

  useEffect(() => {
    isFirstRender.current = true;
  }, [conversation?._id]);

  useEffect(() => {
    const viewport = scrollAreaRef.current;
    if (!viewport) return;

    const scrollToBottom = () => {
      viewport.scrollTo({
        top: viewport.scrollHeight,
        behavior: isFirstRender.current ? "auto" : "smooth", // ✨
      });
    };

    scrollToBottom();
    isFirstRender.current = false;
  }, [messages]);


  const groupMessagesByDate = (messages: Message[]) => {
    const unread = messages.filter(msg => !msg.read && msg.senderId !== user._id);
    const read = messages.filter(msg => msg.read || msg.senderId === user._id);
    const groups: { [key: string]: Message[] } = {};

    read.forEach(message => {
      const dateKey = new Date(message.createdAt).toDateString();
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(message);
    });

    const result = Object.entries(groups).map(([dateKey, messages]) => ({
      groupName: formatDate(dateKey),
      messages
    }));

    if (unread.length > 0)
      result.push({groupName: 'Tin nhắn mới', messages: unread})

    return result
  };

  const messageGroups = groupMessagesByDate(messages);

  if (messages.length === 0 && (user.role === "shop" || user.role === "buyer")) return (
    <div className="flex-1 flex items-center justify-center bg-gradient-to-r from-primary/5 to-primary/10">
          <p className="text-muted-foreground mb-4">
            Gửi một tin nhắn để bắt đầu
          </p>
    </div>
  )

  return (
    <ScrollArea className="flex-1 bg-transparent p-4 " ref={scrollAreaRef}>
      <div

        className="space-y-4"
      >
        {messageGroups.map(({groupName, messages}, index) => (
          <MessageGroup
            key={index}
            showAvatar={showAvatar}
            messages={messages}
            userId={user._id}
            groupName={groupName}
            conversation={conversation}
          />
        ))}
        <div ref={messagesEndRef}/>
      </div>
    </ScrollArea>
  )

}

export default ChatContent;