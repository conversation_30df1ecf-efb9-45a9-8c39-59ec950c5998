import React from "react";
import {Badge} from "@/components/ui/badge.tsx";
import {Message, Conversation} from "@/services/CskhService.ts";
import {cn, formatTime} from '@/lib/utils';
import {CheckCheck} from "lucide-react";
import {Avatar, AvatarFallback, AvatarImage} from "@/components/ui/avatar.tsx";

interface ChatContentProps {
  groupName: string
  messages: Message[]
  showAvatar: boolean
  userId: string
  conversation: Conversation
}

const MessageGroup: React.FC<ChatContentProps> = ({
                                                    groupName,
                                                    messages,
                                                    showAvatar,
                                                    userId,
                                                    conversation,
                                                  }) => {


  const getInitials = (name: string | undefined | null) => {
    if (!name || typeof name !== 'string') {
      return 'KH'; // Khách hàng
    }
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (<div>
    <div className="flex items-center justify-center my-4">
      <Badge variant="outline" className="bg-primary/10 border border-primary/20 text-xs">
        {groupName}
      </Badge>
    </div>

    {/* Messages */}
    {messages.map((msg, index) => {
      const isSender = userId === msg.senderId
      const sender = conversation?.users[msg.senderId]

      return <div
        key={msg._id}
        className={`flex gap-3 my-2 ${isSender ? 'justify-end' : 'justify-start'}`}
      >
        {(showAvatar && !isSender) && (
          <Avatar className="h-8 w-8 mt-1">
            <AvatarImage src={sender?.avatarId ? `/api/files/content/${sender.avatarId}` : undefined} alt={sender?.fullName} />
            <AvatarFallback>{sender?.fullName?.charAt(0)}</AvatarFallback>
          </Avatar>
        )}
        <div
          className={`max-w-[70%] px-3 py-2 border text-slate-600 ${
            isSender
              ? 'border-primary/20 bg-primary/10 rounded-l-xl rounded-tr-xl'
              : 'border-slate-200 bg-slate-100  rounded-r-xl rounded-tl-xl'
          }`}
        >
          <p className={cn("text-sm whitespace-pre-wrap break-words",
            (!isSender && !msg.read) && "font-medium"
          )}>
            {msg.text}
          </p>

          <div
            className="flex items-center gap-1 mt-1"
          >
            <span className={`text-xs ${
              isSender ? 'text-muted-foreground' : 'text-slate-400'
            }`}>
             {formatTime(msg.createdAt)}
            </span>

            {msg.read && isSender && (
              <span className="text-xs h-4 px-1">
                <CheckCheck size={16}/>
              </span>
            )}
          </div>

        </div>

        {showAvatar && isSender && (
          <Avatar className="h-8 w-8 mt-1">
            <AvatarImage src={sender?.avatarId ? `/api/files/content/${sender.avatarId}` : undefined} alt={sender?.fullName} />
            <AvatarFallback>{sender?.fullName?.charAt(0)}</AvatarFallback>
          </Avatar>
        )}

      </div>
    })}
  </div>)

}

export default MessageGroup;