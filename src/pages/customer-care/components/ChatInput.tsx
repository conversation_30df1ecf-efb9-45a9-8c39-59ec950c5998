import React, {useState, useEffect, useRef} from "react";
import {FileText, Image, Plus, Send} from "lucide-react";
import {cn} from '@/lib/utils';
import {bodyMessages} from '@/services/CskhService'

interface ChatInputProps {
  handleSendMessage: (body: bodyMessages) => void;
  disabled?: boolean;
  placeholder?: string;
  handleFocus: () => void;
}

const ChatInput: React.FC<ChatInputProps> = ({
                                               handleSendMessage,
                                               disabled = false,
                                               placeholder = "Nhấn Enter để gửi, Shift + Enter để xuống dòng",
                                               handleFocus
                                             }) => {
  const attachMenuRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [showAttachMenu, setShowAttachMenu] = useState(false);
  const [message, setMessage] = useState('');


  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onClickSend()
    }
  };

  const handleFileSelect = (type: 'image' | 'file') => {
    console.log('Chọn', type);
    setShowAttachMenu(false);
  };

  const toggleAttachMenu = () => {
    setShowAttachMenu(!showAttachMenu);
  };


  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (attachMenuRef.current && !attachMenuRef.current.contains(event.target as Node)) {
        setShowAttachMenu(false);
      }
    };

    if (showAttachMenu) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showAttachMenu]);


  const onClickSend = () => {
    const trimmedMessage = message.trim();
    if (trimmedMessage && !disabled) {
      handleSendMessage({text: trimmedMessage})
      setMessage('')

      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  }


  const handleInputChange = (value: string) => {
    setMessage(value);

    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  };


  return (
    <div className="p-2 border-t border-border bg-transparent rounded-b-lg">
      <div className="flex gap-2 items-end">
        {/* Attachment Button with Click Menu */}
        <div className="relative" ref={attachMenuRef}>
          <button
            onClick={toggleAttachMenu}
            disabled={disabled}
            className={cn("inline-flex items-center justify-center",
              "bg-primary hover:bg-purple-500 rounded-full",
              "transition-colors h-[36px] w-[36px]")}
          >
            <Plus size={16} className="text-slate-300"/>
          </button>

          {/* Click Menu */}
          {showAttachMenu && (
            <div>
              <div
                className={cn("absolute bottom-full bg-slate-200",
                  "shadow-lg rounded-lg",
                  "p-2 min-w-[220px] z-20 mb-4 left-0"
                )}
              >
                <button
                  onClick={() => handleFileSelect('image')}
                  className={cn("flex items-center gap-2 px-3 py-2",
                    "hover:bg-slate-300 w-full text-left",
                    "text-sm text-slate-700 rounded"
                  )}
                >
                  <Image size={16} className="text-purple-600"/>
                  Chọn ảnh có dưới 25MB
                </button>
                <button
                  onClick={() => handleFileSelect('file')}
                  className={cn("flex items-center gap-2 px-3 py-2",
                    "hover:bg-slate-300 w-full text-left",
                    "text-sm text-slate-700 rounded"
                  )}
                >
                  <FileText size={16} className="text-purple-600"/>
                  Chọn tệp đính kèm
                </button>
                {/* SVG Arrow pointing down to button */}
                <svg
                  className="absolute -bottom-2 left-2 w-4 h-2"
                  viewBox="0 0 16 8"
                  fill="none"
                >
                  <path
                    d="M8 8L0 0H16L8 8Z"
                    fill="#E2E8F0"
                    stroke="#E2E8F0"
                    strokeWidth="0"
                  />
                </svg>

              </div>
            </div>
          )}
        </div>

        {/* Message Input */}
        <textarea
          ref={textareaRef}
          value={message}
          disabled={disabled}
          onChange={(e) => handleInputChange(e.target.value)}
          onKeyPress={handleKeyPress}
          onFocus={handleFocus}
          placeholder={placeholder}
          className={cn("flex-1 px-4 py-2 bg-transparent border border-border rounded-lg",
            "focus:outline-none focus:border focus:border-purple-500",
            "text-sm placeholder:text-sm resize-none max-h-20 text-slate-800",
            "placeholder-slate-400 overflow-y-auto scrollbar-wn min-h-[36px]"
          )}
          rows={1}
        />

        {/* Send Button */}
        <button
          onClick={onClickSend}
          disabled={disabled || !message.trim()}
          className={cn("inline-flex items-center justify-center bg-primary text-white",
            "rounded-lg hover:bg-purple-500 transition-colors",
            "text-sm disabled:opacity-50 disabled:cursor-not-allowed h-[36px] w-[50px]")}
        >
          <Send size={16}/>
        </button>
      </div>
    </div>
  )

}

export default ChatInput;