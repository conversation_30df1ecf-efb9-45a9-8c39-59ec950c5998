import {DefaultLayout} from "@/components/layout/DefaultLayout";
import React, {useCallback, useEffect, useState, useRef} from 'react';
import {useAuth} from '@/contexts/AuthContext';
import {useDebouncedSearch} from '@/hooks/use-debounced-search'
import {Conversation, cskhService, Message, bodyMessages} from '@/services/CskhService';
import {SEO} from "@/components/SEO";
import ChatSidebar from '@/pages/customer-care/staff/ChatSidebar';
import ChatHeader from '@/pages/customer-care/staff/ChatHeader';
import ChatContent from '@/pages/customer-care/components/ChatContent.tsx';
import ChatInput from '@/pages/customer-care/components/ChatInput.tsx';
import useSocket from "@/hooks/use-socket.ts";
import {enhancedToast} from "@/components/common/EnhancedToast.tsx";

export default function ChatStaff() {

  const {user} = useAuth();
  const [activeValue, setActiveValue] = useState('open');
  const [conversations, setConversations] = useState<Conversation[]>([]);

  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [messagesLoading, setMessagesLoading] = useState(false);
  const {socket, socketConnected} = useSocket(user._id);

  const activeValueRef = useRef(activeValue);

  useEffect(() => {
    activeValueRef.current = activeValue;
  }, [activeValue]);

  // Initialize socket connection
  useEffect(() => {
    if (!socket) return;


    socket.on("new_message", (data) => {
      setMessages(prevState => {
        return [...prevState, data.message];
      });
    });

    socket.on("notify_conversation", (data) => {
      const isCurrentStatus = data.room.status === activeValueRef.current;
      if (isCurrentStatus)
        setConversations(prevState => {
          return [data.room, ...prevState.filter(e => e._id !== data.room._id)]
        })
    });


    socket.on("update_conversation", (data) => {
      const isCurrentStatus = data.room.status === activeValueRef.current;

      setConversations(prev => {
        const existing = prev.find(item => item._id === data.room._id);

        // Nếu room không còn thuộc status đang lọc → xóa khỏi danh sách
        if (!isCurrentStatus) {
          setMessages([]);
          setActiveConversationId(null);
          return prev.filter(item => item._id !== data.room._id);
        }

        // Nếu đã tồn tại → cập nhật và đẩy lên đầu
        if (existing) {
          const updated = prev
            .map(item => item._id === data.room._id ? {...item, ...data.room} : item)
            .filter(item => item._id !== data.room._id); // bỏ cũ
          return [data.room, ...updated]; // đẩy mới lên đầu
        }

        // Nếu chưa tồn tại → thêm mới
        return [data.room, ...prev];
      });
    });

    socket.on("read_message", (data) => {
      setConversations(prevState => {
        return [data.room, ...prevState.filter(e => e._id !== data.room._id)]
      })
      setMessages(data.messages);
    });

    return () => {
      socket.off()
    };

  }, [socket]);


  useEffect(() => {
    fetchConversations();
  }, [activeValue]);


  const fetchConversations = async () => {
    try {
      setLoading(true);

      const params = {
        pagination: true,
        page: 1,
        limit: 100,
        userId: user._id,
        title: searchQuery,
        status: activeValue
      };

      const result = await cskhService.getConversations(params);
      if (result) {
        if (activeConversationId)
          socket.emit("leave_room", {room_id: activeConversationId});
        setConversations(result.data);
        setActiveConversationId(null)
        setMessages([])
      }

    } catch (error) {
      enhancedToast.error("Không thể tải danh sách cuộc trò chuyện.");
    } finally {
      setLoading(false);
    }
  };


  const fetchMessages = async (conversationId) => {
    try {
      setMessagesLoading(true);

      const params = {
        pagination: true,
        page: 1,
        limit: 100,
      };

      const result = await cskhService.getMessages(conversationId, params);
      if (result) {
        setMessages(result.data.messages);
      }

    } catch (error) {
      enhancedToast.error("Không thể tải nội dung tin nhắn.");
    } finally {
      setMessagesLoading(false);
    }
  };


  const serchConversations = useCallback(() => {
    fetchConversations()
  }, [searchQuery]);

  useDebouncedSearch(searchQuery, 500, serchConversations);

  const handleSelectConversation = async (conversation: Conversation) => {
    if (activeConversationId === conversation._id) return;
    if (activeConversationId) socket.emit("leave_room", {room_id: activeConversationId});

    socket.emit("join_room", {room_id: conversation._id});
    setActiveConversationId(conversation._id);
    if (conversation.unread > 0)
      await cskhService.markReadMessage(conversation._id);
    else
      await fetchMessages(conversation._id)

  };

  const handleSendMessage = async (conversation: Conversation, body: bodyMessages) => {
    try {
      await cskhService.sendMessages(conversation._id, body)
    } catch (error) {
      enhancedToast.error("Không thể gửi tin nhắn.");
    }
  }

  const activeConversation = conversations?.find(conv => conv._id === activeConversationId);

  return (
    <DefaultLayout showFooter={false}>
      <SEO title="Chat với khách hàng" description="Tương tác với khách hàng qua hệ thống chat thời gian thực"/>
      <div className="space-y-6">
        <div className="h-[calc(100vh-8rem)] flex bg-background rounded-lg border border-border overflow-hidden">
          {/* Sidebar */}
          <ChatSidebar
            activeValue={activeValue}
            setActiveValue={setActiveValue}
            conversations={conversations}
            activeConversationId={activeConversationId}
            onSelectConversation={handleSelectConversation}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            loading={loading}
          />

          {/* Main Chat Area */}
          <div className="flex-1 flex flex-col">
            {activeConversationId && <>
                <ChatHeader
                    conversation={activeConversation || null}
                    disabled={!activeConversationId || activeConversation?.status === 'close'}
                />

              {/* Messages */}
                <ChatContent
                    messages={messages}
                    messagesLoading={messagesLoading}
                    conversation={activeConversation}
                    showAvatar={true}
                />

              {/* Input */}
                <ChatInput
                    conversation={activeConversation}
                    handleSendMessage={handleSendMessage}
                    disabled={!activeConversationId || activeConversation?.status === 'close' || activeConversation?.status === 'chatbot'}
                    placeholder="Nhấn Enter để gửi, Shift + Enter để xuống dòng"
                />
            </>}

            {!socketConnected && (
              <div className="px-4 py-2 bg-yellow-50 border-t border-yellow-200 text-yellow-800 text-sm">
                ⚠️ Lỗi kết nối - vui lòng tải lại trang.
              </div>
            )}
          </div>
        </div>
      </div>
    </DefaultLayout>
  );
}
