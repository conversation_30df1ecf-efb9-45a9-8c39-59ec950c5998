import React, {useEffect, useRef} from 'react';
import {Card} from '@/components/ui/card';
import {Button} from '@/components/ui/button';
import {Badge} from '@/components/ui/badge';
import {Bot, Circle, X} from 'lucide-react';

interface ChatStatusSelectorProps {
  currentStatus: string;
  onStatusSelect: (status: string) => void;
  onClose: () => void;
  position?: 'left' | 'right';
}

const statusOptions = [
  {
    value: 'open',
    label: 'Mở',
    description: 'Cuộc trò chuyện đang hoạt động',
    icon: Circle,
    color: 'text-green-600 hover:bg-green-50',
    badgeColor: 'bg-green-100 text-green-800'
  },
  {
    value: 'chatbot',
    label: 'Cha<PERSON> Bot',
    description: '<PERSON><PERSON><PERSON><PERSON> sang chế độ bot tự động',
    icon: Bot,
    color: 'text-blue-600 hover:bg-blue-50',
    badgeColor: 'bg-blue-100 text-blue-800'
  },
  {
    value: 'close',
    label: 'Đóng',
    description: '<PERSON>ế<PERSON> thúc cuộc trò chuyện',
    icon: X,
    color: 'text-red-600 hover:bg-red-50',
    badgeColor: 'bg-red-100 text-red-800'
  },
];

export const ChatStatusSelector: React.FC<ChatStatusSelectorProps> = ({
                                                                        currentStatus,
                                                                        onStatusSelect,
                                                                        onClose,
                                                                        position = 'left'
                                                                      }) => {
  const selectorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectorRef.current && !selectorRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleEscape);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [onClose]);

  const positionClasses = position === 'right'
    ? 'absolute top-full right-0 mt-2'
    : 'absolute top-full left-0 mt-2';

  return (
    <Card
      ref={selectorRef}
      className={`${positionClasses} w-72 p-2 shadow-xl border border-border bg-card z-50 transform transition-all duration-150 ease-out opacity-100 scale-100`}
      style={{
        transformOrigin: position === 'right' ? 'top right' : 'top left'
      }}
    >
      <div className="space-y-1">
        <div className="px-3 py-2 text-sm font-medium text-muted-foreground border-b border-border">
          Thay đổi trạng thái cuộc trò chuyện
        </div>
        {statusOptions.map((option) => {
          const IconComponent = option.icon;
          const isSelected = option.value === currentStatus;

          return (
            <Button
              key={option.value}
              variant="ghost"
              className={`w-full justify-start p-3 h-auto transition-colors duration-150 ${option.color} ${
                isSelected ? 'bg-accent' : ''
              }`}
              onClick={() => onStatusSelect(option.value)}
            >
              <div className="flex items-center gap-3 w-full">
                <IconComponent size={20}/>
                <div className="flex-1 text-left">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{option.label}</span>
                    {isSelected && (
                      <Badge className={option.badgeColor}>Hiện tại</Badge>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {option.description}
                  </p>
                </div>
              </div>
            </Button>
          );
        })}
      </div>
    </Card>
  );
};
