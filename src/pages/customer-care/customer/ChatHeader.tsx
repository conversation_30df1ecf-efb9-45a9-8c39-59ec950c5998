import React from "react";
import {Avatar, AvatarFallback} from "@/components/ui/avatar.tsx";
import {cn} from '@/lib/utils';
import {Conversation} from "@/services/CskhService.ts";
import {MessageCircleX, UserPlus} from "lucide-react";

interface ChatHeaderProps {
  conversation: Conversation;
}

const configStatus = {
  open: {
    bg_color: 'bg-green-500',
    title: "Đã kết nối"
  },
  waiting: {
    bg_color: 'bg-yellow-500',
    title: "Chờ kết nối"
  },
  chatbot: {
    bg_color: 'bg-blue-500',
    title: "Tin nhắn tự động"
  },
  close: {
    bg_color: 'bg-red-500',
    title: "Kết thúc trò chuyện"
  },
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
                                                 conversation,
                                               }) => {

  const status = conversation?.status

  const chatWithStaff = () => {
    console.log('chatWithStaff')
  }

  const closeCurrentConversation = () => {
    console.log('closeCurrentConversation')
  }

  return (<div
      className="flex justify-between items-center p-4 border-b border-border text-black rounded-t-lg">
      <div className="flex items-center gap-3">
        <Avatar className="h-8 w-8">
          <AvatarFallback className="bg-purple-600 text-white text-xs">
            SP
          </AvatarFallback>
        </Avatar>
        <div>
          <h2 className="text-lg font-semibold">Hỗ trợ trực tuyến</h2>
          {conversation && <p className="text-xs text-black">
              <span className={cn(`${configStatus?.[status]?.bg_color}`,
                "inline-block w-2 h-2 rounded-full mr-1")}/>
            {configStatus?.[status]?.title}
          </p>}
        </div>
      </div>

      <div className="flex items-center gap-3">
        {/*{status === 'chatbot' && */}
        <button
          onClick={() => chatWithStaff()}
          className="hover:bg-slate-200 p-2 rounded transition-colors"
        >
          <UserPlus size={20}/>
        </button>
        {/*}*/}


        {/*{status === 'close' && */}
        <button
          onClick={() => closeCurrentConversation()}
          className="hover:bg-slate-200 p-2 rounded transition-colors"
        >
          <MessageCircleX size={20}/>
        </button>
        {/*}*/}

      </div>

    </div>
  )

}

export default ChatHeader;