import React from 'react';
import {Badge} from '@/components/ui/badge';
import {Loader2, Check<PERSON>ircle, <PERSON>r, <PERSON><PERSON>} from 'lucide-react';

const StatusConvo = ({status, staff}) => {
  if (status === 'waiting')
    return (
      <div className="flex items-center space-x-2 font-medium">
        <Loader2 className="h-3 w-3 animate-spin text-orange-600"/>
        <Badge variant="secondary" className="text-orange-600 bg-orange-50">
          Đang kết nối...
        </Badge>
      </div>
    );

  if (status === "open" && staff)
    return (
      <div className="flex items-center space-x-2 font-medium">
        <CheckCircle className="h-3 w-3 text-green-500"/>
        <Badge variant="secondary" className="text-green-600 bg-green-50">
          <User className="h-3 w-3 mr-1"/>
          {staff.fullName}
        </Badge>
      </div>
    );

  if (status === "chatbot")
    return (
      <div className="flex items-center space-x-2 font-medium">
        <CheckCircle className="h-3 w-3 text-blue-500"/>
        <Badge variant="secondary" className="text-blue-600 bg-blue-50">
          <Bot className="h-3 w-3 mr-1"/>
          Chatbot
        </Badge>
      </div>
    );

  if (status === 'close')
    return (
      <div className="flex items-center space-x-2 font-medium">
        <div className="h-3 w-3 bg-red-500 rounded-full"/>
        <Badge variant="secondary" className="text-red-500 bg-red-50">
          Đã đóng
        </Badge>
      </div>
    );
};

export default StatusConvo;
