import React, {useState, useEffect} from 'react';
import {cskhService, Message, Conversation} from '@/services/CskhService';
import ChatHeader from '@/pages/customer-care/customer/ChatHeader.tsx'
import ChatContent from '@/pages/customer-care/components/ChatContent.tsx'
import ChatInput from '@/pages/customer-care/components/ChatInput.tsx'
import {useAuth} from "@/contexts/AuthContext.tsx";
import {toast} from "@/hooks/use-toast.ts";
import useSocket from "@/hooks/use-socket.ts";
import {SEO} from "@/components/SEO.tsx";
import {CustomerLayout} from "@/components/layout/CustomerLayout.tsx";

export default function ChatCustomer() {
  const {user} = useAuth();
  const [conversation, setConversation] = useState<Conversation>(null);
  const [conversationId, setConversationId] = useState<string>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const {socket, socketConnected} = useSocket(user._id);

  useEffect(() => {
    const getData = async () => {
      try {
        const api = await cskhService.getCustomerConversations();

        if (api?.room && api?.messages) {
          setConversation(api.room);
          setConversationId(api.room._id);
          setMessages(api.messages);
        } else {
          console.warn("Không tìm thấy room hoặc messages.");
        }
      } catch (error) {
        console.error("Lỗi khi gọi API getCustomerConversations:", error);
      }
    }

    getData()
  }, []);


  useEffect(() => {
    if (!socket) return;

    socket.on("new_message", (data) => {
      setMessages(prevState => {
        return [...prevState, data.message];
      });
    });

    socket.on("notify_conversation", (data) => {
      setConversation(data.room)
      setConversationId(data.room._id)
    });

    socket.on("update_conversation", (data) => {
      setConversation(data.room)
      setConversationId(data.room._id)
    });

    socket.on("read_message", (data) => {
      setConversation(data.room)
      setMessages(data.messages);
    });

    return () => {
      socket.off()
    };

  }, [socket]);


  useEffect(() => {
    if (conversationId) {
      getMessages().then();
      socket.emit("join_room", {room_id: conversationId});
    }
  }, [conversationId]);


  const getMessages = async () => {
    const params = {
      pagination: true,
      page: 1,
      limit: 100,
    };

    const result = await cskhService.getMessages(conversationId, params);
    if (result) setMessages(result.data.messages)

  }


  const handleSendMessage = async (body, room_id = conversationId) => {
    if (!user) return;
    try {
      if (!room_id) {
        const api = await cskhService.createConversations()
        if (api) {
          setConversationId(api.data._id)
          room_id = api.data._id
        }
      }
      await cskhService.sendMessages(room_id, body)

    } catch (error) {
      console.error('❌ Error sending message:', error);
      toast({
        title: "Lỗi",
        description: "Không thể gửi tin nhắn",
        variant: "destructive",
      });
    }
  }

  const handleFocus = async () => {
    if (conversation && conversation.unread > 0)
      await cskhService.markReadMessage(conversation._id);
  }


  return (
    <CustomerLayout>
      <SEO
        title={`Chat với CSKH`}
        description="Trò chuyện trực tiếp với nhân viên chăm sóc khách hàng"
      />

      <div className="space-y-6">
        {/* Page Header */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-2">
            Chat với CSKH
          </h1>
          <p className="text-muted-foreground">
            Trò chuyện trực tiếp với nhân viên hỗ trợ qua hệ thống chat real-time
          </p>
        </div>

        {/* Chat Interface */}
        <div className="h-[calc(100vh-200px)] border rounded-lg overflow-hidden">

          <div className="flex flex-col h-[calc(100vh-200px)]">

              <ChatHeader
                conversation={conversation}
              />

              <ChatContent
                messages={messages}
                conversation={conversation}
                showAvatar={true}
              />

              <ChatInput
                handleFocus={handleFocus}
                disabled={conversation?.status === 'close'}
                handleSendMessage={handleSendMessage}
              />

          </div>

        </div>
      </div>
    </CustomerLayout>

  );
}
