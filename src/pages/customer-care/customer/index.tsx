import React, {useState, useEffect, useCallback} from 'react';
import {cskhService, Message, Conversation} from '@/services/CskhService';
import ChatHeader from '@/pages/customer-care/customer/ChatHeader'
import ChatContent from '@/pages/customer-care/components/ChatContent.tsx'
import ChatInput from '@/pages/customer-care/components/ChatInput.tsx'
import ChatSidebar from '@/pages/customer-care/customer/ChatSidebar'
import {useAuth} from "@/contexts/AuthContext.tsx";
import useSocket from "@/hooks/use-socket.ts";
import {SEO} from "@/components/SEO.tsx";
import {CustomerLayout} from "@/components/layout/CustomerLayout.tsx";
import {enhancedToast} from "@/components/common/EnhancedToast.tsx";
import {useDebouncedSearch} from "@/hooks/use-debounced-search.ts";

export default function ChatCustomer() {
  const {user} = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversationId, setActiveConversationId] = useState<string>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showNewConvo, setShowNewConvo] = useState(false);
  const [messagesLoading, setMessagesLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTexting, setIsTexting] = useState<boolean>(false);
  const {socket, socketConnected} = useSocket(user._id);

  useEffect(() => {
    fetchConversations().then()
  }, []);

  const fetchConversations = async () => {
    try {
      const api = await cskhService.getCustomerConversations({
        pagination: false,
        title: searchQuery,
      });
      if (api)
        setConversations(api.data)
    } catch (error) {
      enhancedToast.error("Đã có lỗi xảy ra!")
    }
  }


  const addMessageStream = (event, message) => {
    setMessages(prevState => {
      let updatedMessages = [...prevState];
      const lastIndex = updatedMessages.length - 1

      if (event === 'start')
        updatedMessages = [...updatedMessages, message]
      if (event === 'typing' && updatedMessages[lastIndex]?._id === message._id)
        updatedMessages[lastIndex] = message
      if (event === 'end') {
        if(updatedMessages[lastIndex]._id === message._id)
          updatedMessages[lastIndex] = message
        else
          updatedMessages = [...updatedMessages, message]
        setIsTexting(false)
      }

      return updatedMessages
    })
  }

  useEffect(() => {
    if (!socket) return;

    socket.on("new_message", (data) => {
      setMessages(prevState => {
        return [...prevState, data.message];
      })
    });

    socket.on("notify_conversation", (data) => {
      if (data.isCreated) {
        setConversations(prevState => {
          return [data.room, ...prevState];
        })
        setShowNewConvo(false)
        setActiveConversationId(data.room._id)
      } else {
        setConversations(prevState => {
          return [data.room, ...prevState.filter(e => e._id !== data.room._id)]
        })
      }


    });

    socket.on("update_conversation", (data) => {
      const {room} = data
      const isOpen = room.status === 'open' || room.status === 'chatbot' || room.status === 'waiting';
      if (!isOpen) {
        setConversations(prevState => {
          return prevState.filter(e => e._id !== data.room._id)
        })
        setMessages([])
        setActiveConversationId(null)
      } else {
        setConversations(prevState =>
          prevState.map(item =>
            item._id === data.room._id ? {...item, ...data.room} : item
          )
        );
      }
    });

    socket.on("read_message", (data) => {
      setConversations(prevState => {
        return [data.room, ...prevState.filter(e => e._id !== data.room._id)]
      })
      setMessages(data.messages);
    });

    socket.on("bot_streaming", (data) => {
      addMessageStream(data.event, data.message)
    });

    return () => {
      socket.off()
    };

  }, [socket]);


  useEffect(() => {
    if (activeConversationId) {
      getMessages().then();
      socket.emit("join_room", {room_id: activeConversationId});
    }
  }, [activeConversationId]);


  const getMessages = async () => {
    try {
      setMessagesLoading(true);
      const params = {
        pagination: true,
        page: 1,
        limit: 100,
      };

      const result = await cskhService.getMessages(activeConversationId, params);
      if (result) setMessages(result.data.messages)
    } catch (error) {
      enhancedToast.error("Không thể tải nội dung tin nhắn.");
    } finally {
      setMessagesLoading(false);
    }
  }

  const serchConversations = useCallback(() => {
    fetchConversations()
  }, [searchQuery]);

  useDebouncedSearch(searchQuery, 500, serchConversations);


  const handleSendMessage = async (conversation, body) => {
    if (!user) return;
    try {
      if (conversation?.status === 'chatbot')
        setIsTexting(true)
      await cskhService.sendMessages(conversation._id, body)
    } catch (error) {
      enhancedToast.error("Không thể gửi tin nhắn!")
    }
  }


  const handleSelectConversation = async (conversation: Conversation) => {
    if (activeConversationId === conversation._id) return;
    if (activeConversationId) socket.emit("leave_room", {room_id: activeConversationId});
    setActiveConversationId(conversation._id);
    setShowNewConvo(false)
    if (conversation.unread > 0)
      await cskhService.markReadMessage(conversation._id);
  }

  const handleCreateConversation = () => {
    setShowNewConvo(true)
    setMessages([])
    setActiveConversationId(null)
  }

  const activeConversation = conversations?.find(conv => conv._id === activeConversationId);

  return (
    <CustomerLayout>
      <SEO
        title={`Chat với CSKH`}
        description="Trò chuyện trực tiếp với nhân viên chăm sóc khách hàng"
      />

      <div className="space-y-6">

        {/* Chat Interface */}
        <div className="h-[calc(100vh-8rem)] flex bg-background rounded-lg border border-border overflow-hidden">

          <ChatSidebar
            conversations={conversations}
            activeConversationId={activeConversationId}
            onSelectConversation={handleSelectConversation}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            handleCreateConversation={handleCreateConversation}
            // loading={loading}
          />

          <div className="flex-1 flex flex-col">
            {activeConversationId && <ChatHeader
                conversation={activeConversation}
            />}

            <ChatContent
              messages={messages}
              conversation={activeConversation}
              showAvatar={true}
              messagesLoading={messagesLoading}
              showNewConvo={showNewConvo}
            />

            {activeConversationId && <ChatInput
                conversation={activeConversation}
                handleSendMessage={handleSendMessage}
                disabled={activeConversation?.status === 'close' || isTexting || !activeConversationId}
                isTexting={isTexting}
            />}
          </div>
        </div>
      </div>

    </CustomerLayout>

  );
}
