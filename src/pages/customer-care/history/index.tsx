import React, {useState, useEffect} from 'react';
import {cskhService, Message, Conversation} from '@/services/CskhService';
import {useAuth} from "@/contexts/AuthContext.tsx";
import {SEO} from "@/components/SEO.tsx";
import {useNavigate} from 'react-router-dom';
import {ArrowLeft, MessageCircle, BotMessageSquare, User, Calendar, Star} from 'lucide-react';
import {enhancedToast} from "@/components/common/EnhancedToast.tsx";
import {Button} from '@/components/ui/button';
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from '@/components/ui/card';
import {Badge} from '@/components/ui/badge';
import RatingDialog from './RatingDialog/RatingDialog.tsx';
import {CustomerLayout} from "@/components/layout/CustomerLayout.tsx";
import MarkdownRenderer from '@/components/custom/MarkdownRenderer';
import {PaginationBase, PaginationCustom} from "@/components/custom/Pagination";

interface Rating {
  rating: number;
  comment?: string;
}

export default function SupportHistory() {
  const {user} = useAuth();
  const navigate = useNavigate();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<PaginationBase>({
    page: 1,
    limit: 5,
    totalPage: 0
  });
  const [ratingDialog, setRatingDialog] = useState({
    isOpen: false,
    roomId: null,
  });

  useEffect(() => {
    fetchConversations().then();
  }, []);

  const fetchConversations = async (
    page = pagination.page,
    limit = pagination.limit,
    query = {
      title: '',
      status: 'close'
    }) => {
    try {
      setLoading(true);

      const params = {
        ...query,
        pagination: true,
        page: page,
        limit: limit,
        userId: user._id
      };

      const result = await cskhService.getConversations(params);
      if (result) {
        setConversations(result.data);
        setPagination({
          page: result.currentPage,
          limit: pagination.limit,
          totalPage: result.totalPage
        })
      }
    } catch (error) {
      enhancedToast.error("Không thể tải lịch sử hỗ trợ.");
    } finally {
      setLoading(false);
    }
  };

  const onchangePagination = async (newPage) => {
    await fetchConversations(newPage).then()
  }


  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      completed: {label: 'Hoàn thành', variant: 'default'},
      pending: {label: 'Đang xử lý', variant: 'secondary'},
      cancelled: {label: 'Đã hủy', variant: 'destructive'}
    };

    const config = statusConfig[status] || statusConfig.completed;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const handleRating = (roomId) => {
    setRatingDialog({
      isOpen: true,
      roomId,
    });
  };

  const handleCloseRating = () => {
    setRatingDialog({
      isOpen: false,
      roomId: null
    });
  };

  const renderRatingStars = (rating) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-3 w-3 ${
              star <= rating
                ? 'fill-yellow-400 text-yellow-400'
                : 'text-gray-300'
            }`}
          />
        ))}
        <span className="text-xs text-gray-600 ml-1">{rating}/5</span>
      </div>
    );
  };

  return (
    <CustomerLayout>
      <SEO
        title={`Chat với CSKH`}
        description="Trò chuyện trực tiếp với nhân viên chăm sóc khách hàng"
      />

      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-6">
          {/* Header */}
          <Card className="mb-6">
            <CardHeader>
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate('/')}
                >
                  <ArrowLeft className="h-4 w-4"/>
                </Button>
                <div>
                  <CardTitle className="text-xl">Lịch sử hỗ trợ</CardTitle>
                  <CardDescription>
                    Xem lại các cuộc trò chuyện đã hoàn thành
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Chat History List */}
          {conversations.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4"/>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Chưa có lịch sử trò chuyện
                </h3>
                <p className="text-gray-600 mb-4">
                  Các cuộc trò chuyện đã hoàn thành sẽ được hiển thị tại đây
                </p>
                <Button onClick={() => navigate('/shop/support')}>
                  <MessageCircle className="mr-2 h-4 w-4"/>
                  Bắt đầu trò chuyện mới
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {conversations.map((cvs) => {

                const staff = cvs?.users ? Object.values(cvs.users).find((user) => user.role === "cskh") : null;

                return (<Card key={cvs._id} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="grid justify-between grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <CardTitle className="text-lg">Tên chủ đề hỗ trợ</CardTitle>
                          {getStatusBadge(cvs.status)}
                        </div>
                        <div className="flex items-center space-x-6 text-sm text-gray-600">
                          <div className="flex items-center space-x-1">
                            {staff ? <>
                              <User className="h-3 w-3"/>
                              <span>{staff?.fullName}</span>
                            </> : <>
                              <BotMessageSquare className="h-3 w-3"/>
                              <span>Chatbot</span>
                            </>}
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3"/>
                            <span>{formatDate(cvs.createdAt)}</span>
                          </div>
                        </div>


                        {cvs?.feedback && (
                          <div className="mt-2">
                            <div className="text-xs text-gray-500 mb-1">Đánh giá của bạn:</div>
                            {renderRatingStars(cvs?.feedback.rating)}
                            {cvs?.feedback.comment && (
                              <p className="text-xs text-gray-600 mt-1 italic">
                                {cvs?.feedback.comment}
                              </p>
                            )}
                          </div>
                        )}
                      </div>

                      <div className="grid justify-items-start md:justify-items-end space-x-2">
                        <div className="flex items-start space-x-2">
                          <Button variant="outline" size="sm">
                            Xem chi tiết
                          </Button>
                          {!cvs?.feedback && <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleRating(cvs._id)}
                          >
                              <Star className="h-3 w-3 mr-1"/>
                              Đánh giá
                          </Button>}
                        </div>

                      </div>

                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="bg-gray-50 rounded-lg p-3 text-sm">
                      <p className="text-sm text-gray-700 mb-2">
                        <strong>Tin nhắn cuối cùng:</strong>
                      </p>
                      <MarkdownRenderer>
                        {cvs.lastMessage}
                      </MarkdownRenderer>

                    </div>
                  </CardContent>
                </Card>)
              })}
            </div>
          )}

          {pagination.totalPage > 1 && (
            <div className="flex justify-center mt-6">
              <PaginationCustom
                onchangePagination={onchangePagination}
                pagination={pagination}
              />
            </div>
          )}

        </div>

        {/* Rating Dialog */}
        <RatingDialog
          isOpen={ratingDialog.isOpen}
          roomId={ratingDialog.roomId}
          onClose={handleCloseRating}
          fetchConversations={fetchConversations}
        />
      </div>
    </CustomerLayout>

  );
}
