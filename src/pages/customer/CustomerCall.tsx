import { useState } from "react";
import { CustomerLayout } from "@/components/layout/CustomerLayout";
import { SEO } from "@/components/SEO";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertTitle } from "@/components/ui/alert";
import { useAuth } from "@/contexts/AuthContext";
import { CallInterface } from "@/pages/call-center";
import { useSipCall } from "@/pages/call-center";
import { SIP_CONFIG } from "@/pages/call-center";
import { getSipCredentialsByRole, UserRole } from "@/utils/sipConfigByRole";
import {
  PhoneCall,
  User,
  PhoneIncoming,
  Info,
  Lock,
  Headphones
} from "lucide-react";

export default function CustomerCall() {
  const { user } = useAuth();
  const [isConnected, setIsConnected] = useState(false);

  const userRole = (user?.role || 'shop') as UserRole;
  const userTitle = userRole === 'buyer' ? 'Người mua' : 'Cửa hàng';

  // Lấy cấu hình SIP dựa trên role
  const sipCredentials = getSipCredentialsByRole(userRole);
  const [username, setUsername] = useState(sipCredentials.username);
  const [password, setPassword] = useState(sipCredentials.password);

  const handleConnect = (e: React.FormEvent) => {
    e.preventDefault();
    setIsConnected(true);
  };

  return (
    <CustomerLayout>
      <SEO
        title={`Gọi CSKH - ${userTitle}`}
        description="Gọi điện trực tiếp với nhân viên chăm sóc khách hàng"
      />

      <div className="space-y-6">
        {/* Page Header */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-2">
            Gọi nhân viên CSKH
          </h1>
          <p className="text-muted-foreground">
            Liên hệ trực tiếp để được hỗ trợ nhanh chóng qua WebRTC
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Call Interface */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Headphones className="h-5 w-5" />
                  Trung tâm cuộc gọi
                </CardTitle>
              </CardHeader>
              <CardContent>
                {!isConnected ? (
                  <form onSubmit={handleConnect} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="username" className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        Tài khoản SIP
                      </Label>
                      <Input
                        id="username"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        required
                      />
                      <p className="text-sm text-muted-foreground">
                        Tài khoản mặc định cho {userTitle}: {sipCredentials.username}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="password" className="flex items-center gap-2">
                        <Lock className="h-4 w-4" />
                        Mật khẩu
                      </Label>
                      <Input
                        type="password"
                        id="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        required
                      />
                      <p className="text-sm text-muted-foreground">
                        Mật khẩu mặc định cho {userTitle}: {sipCredentials.password}
                      </p>
                    </div>
                    <Button type="submit" className="w-full">
                      <PhoneCall className="mr-2 h-4 w-4" />
                      Kết nối
                    </Button>
                  </form>
                ) : (
                  <CallInterface username={username} password={password} />
                )}
              </CardContent>
            </Card>
          </div>

          {/* Instructions */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  Hướng dẫn sử dụng
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-medium flex items-center gap-2 mb-2">
                    <PhoneCall className="h-4 w-4" />
                    Thực hiện cuộc gọi
                  </h3>
                  <ol className="list-decimal list-inside space-y-1 text-sm">
                    <li>Nhập tài khoản SIP và mật khẩu</li>
                    <li>Nhấn "Kết nối" để kết nối đến server</li>
                    <li>Nhập số điện thoại cần gọi</li>
                    <li>Nhấn "Gọi" để bắt đầu cuộc gọi</li>
                    <li>Sử dụng các nút điều khiển trong cuộc gọi</li>
                  </ol>
                </div>

                <div>
                  <h3 className="font-medium flex items-center gap-2 mb-2">
                    <PhoneIncoming className="h-4 w-4" />
                    Nhận cuộc gọi
                  </h3>
                  <ol className="list-decimal list-inside space-y-1 text-sm">
                    <li>Khi có cuộc gọi đến, nhấn "Trả lời"</li>
                    <li>Hoặc nhấn "Từ chối" để từ chối cuộc gọi</li>
                    <li>Sử dụng các nút điều khiển trong cuộc gọi</li>
                  </ol>
                </div>

                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>Lưu ý</AlertTitle>
                  <div className="text-sm mt-2">
                    <p>• Cần cho phép truy cập microphone</p>
                    <p>• Đảm bảo kết nối internet ổn định</p>
                    <p>• Sử dụng tai nghe để chất lượng tốt nhất</p>
                  </div>
                </Alert>

                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Các số máy chăm sóc khách hàng:</h4>
                  <div className="text-sm space-y-1">
                    <div>• 1001 - CSKH 1</div>
                    <div>• 1002 - CSKH 2</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </CustomerLayout>
  );
}
