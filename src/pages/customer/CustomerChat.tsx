import { CustomerLayout } from "@/components/layout/CustomerLayout";
import { SEO } from "@/components/SEO";
import { useAuth } from "@/contexts/AuthContext";

export default function CustomerChat() {
  const { user } = useAuth();
  const userRole = user?.role || 'shop';
  const userTitle = userRole === 'buyer' ? 'Người mua' : 'Cửa hàng';



  return (
    <CustomerLayout>
      <SEO
        title={`Chat với CSKH - ${userTitle}`}
        description="Trò chuyện trực tiếp với nhân viên chăm sóc khách hàng"
      />

      <div className="space-y-6">
        {/* Page Header */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-2">
            Chat với CSKH
          </h1>
          <p className="text-muted-foreground">
            T<PERSON><PERSON> chuyện trực tiếp với nhân viên hỗ trợ qua hệ thống chat real-time
          </p>
        </div>

        {/* Chat Interface */}
        <div className="h-[calc(100vh-200px)] border rounded-lg overflow-hidden">
        </div>
      </div>
    </CustomerLayout>
  );
}
