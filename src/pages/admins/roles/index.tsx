import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Edit, Trash2, Shield } from "lucide-react";

// Import các common component
import { DataTable } from "@/components/common/DataTable";
import { SearchBar } from "@/components/common/SearchBar";
import { PageHeader, PageHeaderAction } from "@/components/common/PageHeader";
import { ConfirmDeleteDialog } from "@/components/common/ConfirmDeleteDialog";

export default function Roles() {
  const [searchQuery, setSearchQuery] = useState("");

  // Dữ liệu mẫu cho danh sách nhóm quyền
  const roles = [
    {
      id: 1,
      name: "Admin",
      description: "Toàn quyền quản trị hệ thống",
      permissions: "Tất cả quyền",
    },
    {
      id: 2,
      name: "CSKH",
      description: "Chăm sóc khách hàng",
      permissions: "Chat với khách hàng, nghe cuộc gọi, xem lịch sử cuộc gọi",
    },
    {
      id: 3,
      name: "<PERSON><PERSON><PERSON><PERSON> hàng",
      description: "<PERSON><PERSON><PERSON><PERSON> hàng trực tuyến",
      permissions: "Chat với CSKH, nghe cuộc gọi, xem lịch sử cuộc gọi",
    },
  ];

  // Lọc danh sách nhóm quyền theo từ khóa tìm kiếm
  const filteredRoles = roles.filter(role =>
    role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    role.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <PageHeader
        title="Nhóm quyền"
        description="Quản lý các nhóm quyền trong hệ thống"
        actions={ <></>
          // <PageHeaderAction
          //   icon={<Plus className="h-4 w-4" />}
          //   text="Thêm nhóm quyền"
          //   onClick={() => {}}
          // />
        }
      />

      {/* Thanh tìm kiếm */}
      <SearchBar
        value={searchQuery}
        onChange={(value) => setSearchQuery(value)}
        placeholder="Tìm kiếm nhóm quyền..."
        showClearButton={true}
        useDebounce={true}
        debounceTime={300}
      />

      {/* Bảng danh sách nhóm quyền */}
      <DataTable
        data={filteredRoles}
        loading={false}
        emptyMessage="Không tìm thấy nhóm quyền nào"
        columns={[
          {
            header: "STT",
            cell: (_, index) => index + 1,
            className: "w-[50px]"
          },
          {
            header: "Tên nhóm",
            cell: (role) => (
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-primary" />
                <span>{role.name}</span>
              </div>
            )
          },
          {
            header: "Mô tả",
            accessorKey: "description"
          },
          {
            header: "Quyền hạn",
            accessorKey: "permissions"
          },
          // {
          //   header: "Thao tác",
          //   className: "text-right",
          //   cell: (role) => (
          //     <div className="flex justify-end gap-2">
          //       <Button
          //         variant="ghost"
          //         size="icon"
          //         title="Chỉnh sửa"
          //       >
          //         <Edit className="h-4 w-4"/>
          //       </Button>
          //       <Button
          //         variant="ghost"
          //         size="icon"
          //         title="Xóa"
          //       >
          //         <Trash2 className="h-4 w-4 text-destructive"/>
          //       </Button>
          //     </div>
          //   )
          // }
        ]}
      />
    </div>
  );
}
