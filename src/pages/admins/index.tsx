import { useState } from "react";
import { DefaultLayout } from "@/components/layout/DefaultLayout";
import { SEO } from "@/components/SEO";
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { UserCog, Shield } from "lucide-react";

// Import common components
import { PageHeader } from "@/components/common/PageHeader";

// Import các component con
import Administrators from "./administrators";
import Roles from "./roles";

export default function Admins() {
  const [activeTab, setActiveTab] = useState("overview");

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  return (
    <DefaultLayout>
      <SEO title="Quản trị hệ thống" description="Quản lý người dùng và phân quyền" />
      <div className="space-y-6">
        <PageHeader
          title="Quản trị hệ thống"
          description="Quản lý người dùng và phân quyền trong hệ thống"
        />

        <Tabs
          value={activeTab}
          onValueChange={handleTabChange}
          className="w-full"
        >
          <TabsList className="grid w-full md:w-[600px] grid-cols-3">
            <TabsTrigger
              value="overview"
              className="data-[state=active]:border-b-2 data-[state=active]:border-primary"
            >
              Tổng quan
            </TabsTrigger>
            <TabsTrigger
              value="administrators"
              className="data-[state=active]:border-b-2 data-[state=active]:border-primary"
            >
              Người dùng
            </TabsTrigger>
            <TabsTrigger
              value="roles"
              className="data-[state=active]:border-b-2 data-[state=active]:border-primary"
            >
              Nhóm quyền
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-6">
            <div className="grid gap-4 md:grid-cols-2">
              <Card className="border shadow-sm hover:shadow-md transition-shadow">
                <CardContent className="p-6 flex items-start gap-4">
                  <div className="rounded-full bg-blue-100 p-3">
                    <UserCog className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="space-y-1">
                    <h3 className="text-xl font-medium">Quản lý người dùng</h3>
                    <p className="text-muted-foreground">
                      Quản lý danh sách người dùng trong hệ thống
                    </p>
                    <button
                      className="text-primary hover:underline mt-2 font-medium"
                      onClick={() => handleTabChange("administrators")}
                    >
                      Xem chi tiết
                    </button>
                  </div>
                </CardContent>
              </Card>

              <Card className="border shadow-sm hover:shadow-md transition-shadow">
                <CardContent className="p-6 flex items-start gap-4">
                  <div className="rounded-full bg-purple-100 p-3">
                    <Shield className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="space-y-1">
                    <h3 className="text-xl font-medium">Nhóm quyền</h3>
                    <p className="text-muted-foreground">
                      Quản lý các nhóm quyền và phân quyền trong hệ thống
                    </p>
                    <button
                      className="text-primary hover:underline mt-2 font-medium"
                      onClick={() => handleTabChange("roles")}
                    >
                      Xem chi tiết
                    </button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="administrators" className="mt-6">
            <Administrators />
          </TabsContent>

          <TabsContent value="roles" className="mt-6">
            <Roles />
          </TabsContent>
        </Tabs>
      </div>
    </DefaultLayout>
  );
}
