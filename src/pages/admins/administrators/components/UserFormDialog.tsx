import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Loader2,
  Mail,
  Phone,
  User as UserIcon,
  Lock,
  Eye,
  EyeOff,
  CheckCircle,
  XCircle
} from "lucide-react";
import { enhancedToast } from "@/components/common/EnhancedToast";
import { userService, User, CreateUserData, UpdateUserData } from "@/services/UserService";

// Interface cho form thêm/sửa người dùng
interface UserFormValues {
  fullName: string;
  email: string;
  password?: string;
  phone?: string;
  gender?: string;
  role: string;
}

interface UserFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user?: User;
  onSuccess: () => void;
}

export function UserFormDialog({
  open,
  onOpenChange,
  user,
  onSuccess
}: UserFormDialogProps) {
  const isEditing = !!user;
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<UserFormValues>({
    defaultValues: {
      fullName: user?.fullName || "",
      email: user?.email || "",
      password: "",
      phone: user?.phone || "",
      gender: user?.gender || "",
      role: user?.role || "admin",
    }
  });

  // Reset form khi mở dialog với user khác
  useEffect(() => {
    if (open) {
      form.reset({
        fullName: user?.fullName || "",
        email: user?.email || "",
        password: "",
        phone: user?.phone || "",
        gender: user?.gender || "",
        role: user?.role || "admin",
      });
    }
  }, [open, user, form]);

  const onSubmit = async (data: UserFormValues) => {
    try {
      setIsSubmitting(true);

      if (isEditing && user?._id) {
        // Cập nhật người dùng
        const updateData: UpdateUserData = {
          fullName: data.fullName,
          email: data.email,
          phone: data.phone,
          gender: data.gender,
          role: data.role,
        };

        // Chỉ gửi password nếu có nhập
        if (data.password) {
          updateData.password = data.password;
        }

        await userService.updateUser(user._id, updateData);
        enhancedToast.success("Cập nhật người dùng thành công!", {
          icon: <CheckCircle className="h-5 w-5" />,
          duration: 4000,
          closeButton: true
        });
      } else {
        // Tạo mới người dùng
        const createData: CreateUserData = {
          fullName: data.fullName,
          email: data.email,
          password: data.password || "123456", // Mật khẩu mặc định nếu không nhập
          phone: data.phone,
          gender: data.gender,
          role: data.role,
        };

        await userService.createUser(createData);
        enhancedToast.success("Thêm người dùng thành công!", {
          icon: <CheckCircle className="h-5 w-5" />,
          duration: 4000,
          closeButton: true
        });
      }

      // Đóng dialog và refresh danh sách
      onOpenChange(false);
      onSuccess();
    } catch (error: any) {
      enhancedToast.error(error.message || "Đã xảy ra lỗi khi lưu dữ liệu!", {
        icon: <XCircle className="h-5 w-5" />,
        duration: 5000,
        closeButton: true
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Chỉnh sửa người dùng" : "Thêm mới người dùng"}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Cập nhật thông tin người dùng trong hệ thống."
              : "Thêm mới người dùng vào hệ thống."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Họ và tên <span className="text-red-500">*</span></FormLabel>
                  <div className="relative">
                    <UserIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <FormControl>
                      <Input className="pl-10" placeholder="Nguyễn Văn A" {...field} required />
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email <span className="text-red-500">*</span></FormLabel>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <FormControl>
                      <Input
                        type="email"
                        className="pl-10"
                        placeholder="<EMAIL>"
                        {...field}
                        required
                      />
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{isEditing ? "Mật khẩu mới" : "Mật khẩu"} {!isEditing && <span className="text-red-500">*</span>}</FormLabel>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <FormControl>
                      <Input
                        type={showPassword ? "text" : "password"}
                        className="pl-10 pr-10"
                        placeholder={isEditing ? "Để trống nếu không đổi" : "Nhập mật khẩu"}
                        {...field}
                        required={!isEditing}
                      />
                    </FormControl>
                    <div
                      className="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <Eye className="h-4 w-4 text-muted-foreground" />
                      )}
                    </div>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Số điện thoại</FormLabel>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <FormControl>
                      <Input className="pl-10" placeholder="0123456789" {...field} />
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Vai trò <span className="text-red-500">*</span></FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value} required>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Chọn vai trò" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="admin">Quản trị viên</SelectItem>
                        <SelectItem value="cskh">Quản lý</SelectItem>
                        <SelectItem value="shop">Khách hàng - Shop</SelectItem>
                        <SelectItem value="buyer">Khách hàng - Người mua hàng</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/*<FormField*/}
              {/*  control={form.control}*/}
              {/*  name="status"*/}
              {/*  render={({ field }) => (*/}
              {/*    <FormItem>*/}
              {/*      <FormLabel>Trạng thái <span className="text-red-500">*</span></FormLabel>*/}
              {/*      <Select onValueChange={field.onChange} defaultValue={field.value} required>*/}
              {/*        <FormControl>*/}
              {/*          <SelectTrigger>*/}
              {/*            <SelectValue placeholder="Chọn trạng thái" />*/}
              {/*          </SelectTrigger>*/}
              {/*        </FormControl>*/}
              {/*        <SelectContent>*/}
              {/*          <SelectItem value="active">Hoạt động</SelectItem>*/}
              {/*          <SelectItem value="inactive">Không hoạt động</SelectItem>*/}
              {/*        </SelectContent>*/}
              {/*      </Select>*/}
              {/*      <FormMessage />*/}
              {/*    </FormItem>*/}
              {/*  )}*/}
              {/*/>*/}
            </div>

            <DialogFooter className="mt-6">
              <DialogClose asChild>
                <Button type="button" variant="outline">Hủy</Button>
              </DialogClose>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditing ? "Cập nhật" : "Thêm mới"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
