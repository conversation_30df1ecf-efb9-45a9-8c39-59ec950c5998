/**
 * Service xử lý Call History và tích hợp với backend
 */

import { api } from '@/lib/api';
import {
  CallRecord,
  CallHistoryParams,
  CallHistoryResponse,
  StartCallRequest,
  StartCallResponse,
  EndCallRequest,
  EndCallResponse
} from '../types/callHistory';

class CallHistoryService {
  private baseUrl = '/call-history';

  /**
   * Bắt đầu ghi lại cuộc gọi
   */
  async startCall(params: StartCallRequest): Promise<StartCallResponse> {
    try {
      const response = await api.post<StartCallResponse>(`${this.baseUrl}/start`, params);
      console.log('Call started:', response);
      return response;
    } catch (error) {
      console.error('Failed to start call recording:', error);
      throw error;
    }
  }

  /**
   * Kết thúc cuộc gọi và upload audio
   */
  async endCall(params: EndCallRequest): Promise<EndCallResponse> {
    try {
      // N<PERSON><PERSON> có audio blob, upload file trước
      let audioUrl: string | undefined;
      
      if (params.audioBlob) {
        const uploadResult = await this.uploadAudioFile(params.callId, params.audioBlob);
        audioUrl = uploadResult.url;
      }

      const response = await api.post<EndCallResponse>(`${this.baseUrl}/end`, {
        ...params,
        audioUrl
      });
      
      console.log('Call ended:', response);
      return response;
    } catch (error) {
      console.error('Failed to end call:', error);
      throw error;
    }
  }

  /**
   * Upload file audio
   */
  async uploadAudioFile(callId: string, audioBlob: Blob): Promise<{ url: string; fileId: string; size: number }> {
    try {
      const formData = new FormData();
      formData.append('file', audioBlob, `call_${callId}_${Date.now()}.wav`);
      formData.append('folder', 'call-recordings');

      const response = await fetch('/upload/file', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const result = await response.json();
      return {
        url: result.url,
        fileId: result.fileId || result.id,
        size: audioBlob.size
      };
    } catch (error) {
      console.error('Failed to upload audio file:', error);
      throw error;
    }
  }

  /**
   * Lấy lịch sử cuộc gọi với phân trang
   */
  async getCallHistory(params: CallHistoryParams = {}): Promise<CallHistoryResponse> {
    try {
      const queryParams = {
        page: params.page || 1,
        limit: params.limit || 20,
        ...(params.agentId && { agentId: params.agentId }),
        ...(params.dateFrom && { dateFrom: params.dateFrom }),
        ...(params.dateTo && { dateTo: params.dateTo }),
        ...(params.callType && { callType: params.callType }),
        ...(params.callStatus && { callStatus: params.callStatus })
      };

      const response = await api.get<CallHistoryResponse>(this.baseUrl, { 
        params: queryParams 
      });
      
      return response;
    } catch (error) {
      console.error('Failed to fetch call history:', error);
      throw error;
    }
  }

  /**
   * Lấy chi tiết một cuộc gọi
   */
  async getCallById(callId: string): Promise<CallRecord> {
    try {
      const response = await api.get<CallRecord>(`${this.baseUrl}/${callId}`);
      return response;
    } catch (error) {
      console.error('Failed to fetch call details:', error);
      throw error;
    }
  }

  /**
   * Cập nhật thông tin cuộc gọi
   */
  async updateCall(callId: string, updates: Partial<CallRecord>): Promise<CallRecord> {
    try {
      const response = await api.put<CallRecord>(`${this.baseUrl}/${callId}`, updates);
      return response;
    } catch (error) {
      console.error('Failed to update call:', error);
      throw error;
    }
  }

  /**
   * Xóa cuộc gọi
   */
  async deleteCall(callId: string): Promise<void> {
    try {
      await api.delete(`${this.baseUrl}/${callId}`);
    } catch (error) {
      console.error('Failed to delete call:', error);
      throw error;
    }
  }

  /**
   * Lấy thống kê cuộc gọi
   */
  async getCallStats(params: { 
    dateFrom?: string; 
    dateTo?: string; 
    agentId?: string 
  } = {}): Promise<{
    totalCalls: number;
    completedCalls: number;
    missedCalls: number;
    aiHandledCalls: number;
    averageDuration: number;
    totalDuration: number;
  }> {
    try {
      const response = await api.get(`${this.baseUrl}/stats`, { params });
      return response;
    } catch (error) {
      console.error('Failed to fetch call stats:', error);
      throw error;
    }
  }

  /**
   * Tìm kiếm cuộc gọi
   */
  async searchCalls(query: string, params: CallHistoryParams = {}): Promise<CallHistoryResponse> {
    try {
      const searchParams = {
        ...params,
        query,
        page: params.page || 1,
        limit: params.limit || 20
      };

      const response = await api.get<CallHistoryResponse>(`${this.baseUrl}/search`, { 
        params: searchParams 
      });
      
      return response;
    } catch (error) {
      console.error('Failed to search calls:', error);
      throw error;
    }
  }

  /**
   * Export lịch sử cuộc gọi
   */
  async exportCallHistory(params: CallHistoryParams & { format: 'csv' | 'excel' }): Promise<Blob> {
    try {
      const response = await fetch(`${this.baseUrl}/export?${new URLSearchParams(params as any)}`, {
        method: 'GET',
        headers: {
          'Accept': params.format === 'csv' ? 'text/csv' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
      });

      if (!response.ok) {
        throw new Error(`Export failed: ${response.statusText}`);
      }

      return await response.blob();
    } catch (error) {
      console.error('Failed to export call history:', error);
      throw error;
    }
  }

  /**
   * Tạo call ID duy nhất
   */
  generateCallId(): string {
    return `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Tính toán thời lượng cuộc gọi
   */
  calculateDuration(startTime: Date, endTime: Date): number {
    return Math.floor((endTime.getTime() - startTime.getTime()) / 1000);
  }

  /**
   * Format thông tin người gọi
   */
  formatCallerInfo(callerNumber: string, customerInfo?: { name?: string }): string {
    if (customerInfo?.name) {
      return `${customerInfo.name} (${callerNumber})`;
    }
    return callerNumber;
  }

  /**
   * Kiểm tra quyền truy cập cuộc gọi
   */
  canAccessCall(call: CallRecord, currentUserId?: string): boolean {
    // Logic kiểm tra quyền truy cập
    if (!currentUserId) return false;
    if (call.agentId === currentUserId) return true;
    // Admin có thể truy cập tất cả cuộc gọi
    return true; // Tạm thời cho phép tất cả
  }
}

// Export singleton instance
export const callHistoryService = new CallHistoryService();
export default callHistoryService;
