import {DefaultLayout} from "@/components/layout/DefaultLayout";
import {SEO} from "@/components/SEO";
import CallCenterPage from "./pages/CallCenterPage";

export default function CallCenter() {
  return (
    <DefaultLayout>
      <SEO title="Trung tâm cuộc gọi" description="Quản lý cuộc gọi khách hàng qua WebRTC và SIP"/>
      <CallCenterPage/>
    </DefaultLayout>
  );
}

// Export các thành phần chính
export {default as CallCenterPage} from './pages/CallCenterPage';
export {default as CallInterface} from './components/CallInterface';
export {useSipCall} from './hooks/useSipCall';
export {default as sipService} from './services/sipService';
export {default as ringToneService} from './services/ringToneService';
export {SIP_CONFIG} from './config/sipConfig';
export type {CallState} from './services/sipService';
