/**
 * <PERSON><PERSON><PERSON> hình cho tính năng ghi âm cuộc gọi
 */

export const RECORDING_CONFIG = {
  // Cài đặt MediaRecorder
  RECORDER_OPTIONS: {
    mimeType: 'audio/webm;codecs=opus',
    audioBitsPerSecond: 64000
  },

  // Các định dạng audio được hỗ trợ (fallback)
  SUPPORTED_MIME_TYPES: [
    'audio/webm;codecs=opus',
    'audio/webm',
    'audio/mp4',
    'audio/wav'
  ],

  // Cài đặt ghi âm
  AUTO_RECORDING_ENABLED: true, // Mặc định bật tự động ghi âm
  CHUNK_INTERVAL: 1000, // Lưu chunk mỗi 1 giây
  MAX_RECORDING_DURATION: 3600, // Tối đa 1 giờ (giây)
  MAX_FILE_SIZE: 50 * 1024 * 1024, // Tối đa 50MB

  // Cài đặt file
  FILE_PREFIX: 'call-recording',
  FILE_EXTENSION: '.webm',
  
  // Delay trước khi bắt đầu ghi âm tự động (ms)
  AUTO_START_DELAY: 500,

  // Thông báo
  MESSAGES: {
    AUTO_START: '🔴 Tự động bắt đầu ghi âm cuộc gọi',
    AUTO_STOP: '⏹️ Tự động dừng ghi âm cuộc gọi',
    MANUAL_START: '🔴 Bắt đầu ghi âm thủ công',
    MANUAL_STOP: '⏹️ Dừng ghi âm thủ công',
    SAVE_SUCCESS: '💾 File ghi âm đã được lưu',
    SAVE_ERROR: '❌ Lỗi khi lưu file ghi âm',
    NO_STREAM: '⚠️ Không có stream âm thanh để ghi âm',
    BROWSER_NOT_SUPPORTED: '⚠️ Trình duyệt không hỗ trợ ghi âm'
  }
};

/**
 * Kiểm tra browser có hỗ trợ ghi âm không
 */
export const checkRecordingSupport = (): {
  supported: boolean;
  mimeType: string | null;
  error?: string;
} => {
  // Kiểm tra MediaRecorder API
  if (!window.MediaRecorder) {
    return {
      supported: false,
      mimeType: null,
      error: 'MediaRecorder API không được hỗ trợ'
    };
  }

  // Tìm mime type được hỗ trợ
  for (const mimeType of RECORDING_CONFIG.SUPPORTED_MIME_TYPES) {
    if (MediaRecorder.isTypeSupported(mimeType)) {
      return {
        supported: true,
        mimeType
      };
    }
  }

  return {
    supported: false,
    mimeType: null,
    error: 'Không có định dạng audio nào được hỗ trợ'
  };
};

/**
 * Tạo tên file ghi âm với timestamp
 */
export const generateRecordingFileName = (phoneNumber?: string): string => {
  const timestamp = new Date().getTime();
  const phone = phoneNumber ? `-${phoneNumber}` : '';
  return `${RECORDING_CONFIG.FILE_PREFIX}-${timestamp}${phone}${RECORDING_CONFIG.FILE_EXTENSION}`;
};

/**
 * Format kích thước file
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Format thời lượng ghi âm
 */
export const formatRecordingDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
};

/**
 * Kiểm tra kích thước file có vượt quá giới hạn không
 */
export const isFileSizeExceeded = (size: number): boolean => {
  return size > RECORDING_CONFIG.MAX_FILE_SIZE;
};

/**
 * Kiểm tra thời lượng ghi âm có vượt quá giới hạn không
 */
export const isDurationExceeded = (duration: number): boolean => {
  return duration > RECORDING_CONFIG.MAX_RECORDING_DURATION;
};
