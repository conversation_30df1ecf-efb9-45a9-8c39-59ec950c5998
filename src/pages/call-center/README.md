# Trang Trung Tâm Cuộc Gọi (Call Center)

## Tổng quan

Trang **Trung tâm cuộc gọi** là một tính năng quan trọng của hệ thống CSKH (Chăm sóc khách hàng), cho phép nhân viên thực hiện và nhận cuộc gọi trực tiếp từ trình duyệt web thông qua công nghệ WebRTC và giao thức SIP.

## Cấu trúc thư mục

```
src/pages/call-center/
├── components/
│   └── CallInterface.tsx      # Giao diện chính cho việc gọi điện
├── config/
│   └── sipConfig.ts          # Cấu hình kết nối SIP
├── hooks/
│   └── useSipCall.ts         # Custom hook quản lý cuộc gọi SIP
├── pages/
│   └── CallCenterPage.tsx    # Trang chính của call center
├── services/
│   ├── sipService.ts         # Service xử lý kết nối và cuộc gọi SIP
│   └── ringToneService.ts    # Service quản lý âm thanh chuông báo cuộc gọi
└── index.tsx                 # Entry point và exports
```

## Tính năng chính

### 1. Kết nối SIP
- Hỗ trợ kết nối đến máy chủ SIP thông qua WebSocket
- Xác thực người dùng với username/password
- Tự động đăng ký và duy trì kết nối

### 2. Thực hiện cuộc gọi
- **Gọi với microphone**: Cuộc gọi hai chiều đầy đủ
- **Chỉ nghe**: Cuộc gọi một chiều (chỉ nghe, không nói)
- Hiển thị trạng thái cuộc gọi theo thời gian thực
- Đếm thời gian cuộc gọi

### 3. Nhận cuộc gọi
- Thông báo cuộc gọi đến với chuông báo âm thanh
- Tùy chọn trả lời hoặc từ chối
- **📞 Hiển thị thông tin người gọi**:
  - Tự động hiển thị "Cuộc gọi đến từ khách hàng"
  - Nếu có thông tin số điện thoại: "Cuộc gọi đến từ khách hàng (0123456789)"
  - Nếu có display name: "Cuộc gọi đến từ [Tên khách hàng]"
- **🔔 Phát chuông báo tự động**: Khi có cuộc gọi đến, hệ thống sẽ tự động phát chuông báo để người dùng dễ dàng nhận biết

### 4. Điều khiển cuộc gọi
- **Tắt/bật microphone**: Mute/unmute trong cuộc gọi
- **Ghi âm**: Bắt đầu/dừng ghi âm cuộc gọi
- **Kết thúc cuộc gọi**: Hangup cuộc gọi hiện tại

### 5. Xử lý lỗi thông minh
- Phát hiện và xử lý lỗi microphone
- Thông báo lỗi kết nối SIP chi tiết
- Gợi ý giải pháp cho từng loại lỗi

## Các thành phần chính

### CallCenterPage.tsx
Trang chính chứa:
- Form cấu hình tài khoản SIP
- Giao diện cuộc gọi chính
- Hướng dẫn sử dụng
- Danh sách các số máy nhánh

### CallInterface.tsx
Component giao diện cuộc gọi bao gồm:
- Form nhập số điện thoại
- Các nút điều khiển cuộc gọi
- Hiển thị trạng thái và thời gian cuộc gọi
- Xử lý cuộc gọi đến/đi

### useSipCall.ts
Custom hook cung cấp:
- Quản lý trạng thái cuộc gọi
- Các phương thức điều khiển cuộc gọi
- Callback xử lý sự kiện

### sipService.ts
Service chính xử lý:
- Kết nối SIP server
- Thực hiện/nhận cuộc gọi
- Quản lý media stream
- Xử lý lỗi và cleanup
- Tích hợp với ringToneService để phát chuông báo

### ringToneService.ts
Service quản lý âm thanh chuông báo:
- Tạo âm thanh chuông báo bằng Web Audio API
- Phát chuông liên tục khi có cuộc gọi đến
- Tự động dừng chuông khi trả lời/từ chối cuộc gọi
- Điều chỉnh âm lượng chuông báo
- Xử lý graceful khi không hỗ trợ Web Audio API

## Cấu hình

### Cấu hình tài khoản theo Role

Hệ thống tự động sử dụng tài khoản SIP phù hợp dựa trên role của người dùng:

#### CSKH và Admin
- **Tài khoản**: `cskh-1`
- **Mật khẩu**: `abc123@1`
- **Sử dụng tại**: `/call-center` (CallCenterPage)

#### Shop và Buyer
- **Tài khoản**: `kh-1`
- **Mật khẩu**: `abc123`
- **Sử dụng tại**: `/shop/call`, `/buyer/call` (CustomerCall)
- **Tính năng**: Gọi đến số máy CSKH, không có lịch sử cuộc gọi

### Cấu trúc cấu hình
Hệ thống sử dụng cấu hình phân tầng với các file môi trường:

```
├── .env                    # Cấu hình chung cho tất cả môi trường
├── .env.development        # Cấu hình cho môi trường development
├── .env.production         # Cấu hình cho môi trường production
├── .env.local             # Cấu hình local (override, không commit)
└── src/config/env.ts      # File quản lý cấu hình tập trung
```

### Biến môi trường SIP (Cấu hình thực tế)

#### .env (Cấu hình chung)
```bash
# API URLs
VITE_API_BASE_URL=/api
VITE_CHAT_URL=/cskh
VITE_UPLOAD_URL=/upload/file
VITE_FILE_CONTENT_URL=/api/files/content

# App Info
VITE_APP_NAME=GHVN - Hệ thống quản trị
VITE_APP_DESCRIPTION=GHVN - Hệ thống quản trị
VITE_APP_KEYWORDS=GHVN, quản trị, helpdesk, customer service
VITE_APP_LOGO=/assets/images/logo/logo_ghvn.png
```

#### .env.development (Development)
```bash
# API Backend
VITE_API_BACKEND_URL=http://localhost:3000
VITE_API_CHAT_URL=http://localhost:5055
VITE_API_SOCKET_URL=ws://localhost:5055

# SIP Config
VITE_SIP_SERVER_URL=***************:5090
VITE_SIP_WEBSOCKET_URL=ws://***************:8088/ws
VITE_SIP_DEFAULT_USER=e1
VITE_SIP_DEFAULT_PASSWORD=Xproz2025
VITE_SIP_MAIN_SERVER=**************:5060
VITE_SIP_BACKUP_SERVER=***************:5060
VITE_SIP_SIGNALING_PORT=5060
VITE_SIP_RTP_PORT_RANGE=10000-65535
VITE_SIP_FROM_PREFIX=09
VITE_SIP_TO_PREFIX=9

# Debug
VITE_ENABLE_API_LOGS=true
```

#### .env.production (Production)
```bash
# API Backend
VITE_API_BACKEND_URL=https://api.ghvn.vn

# SIP Config
VITE_SIP_SERVER_URL=***************:5090
VITE_SIP_WEBSOCKET_URL=wss://sip.ghvn.vn/ws

# Tài khoản SIP cho CSKH/Admin
VITE_SIP_DEFAULT_USER=cskh-1
VITE_SIP_DEFAULT_PASSWORD=abc123@1

# Tài khoản SIP cho khách hàng (shop/buyer)
VITE_SIP_CUSTOMER_USER=kh-1
VITE_SIP_CUSTOMER_PASSWORD=abc123

VITE_SIP_MAIN_SERVER=**************:5060
VITE_SIP_BACKUP_SERVER=***************:5060
VITE_SIP_SIGNALING_PORT=5060
VITE_SIP_RTP_PORT_RANGE=10000-65535
VITE_SIP_FROM_PREFIX=09
VITE_SIP_TO_PREFIX=9

# Debug
VITE_ENABLE_API_LOGS=false
```

### Cấu hình trong code (src/config/env.ts)

#### Hàm helper
```typescript
const getEnvVariable = (key: string, defaultValue: string = ''): string => {
  return import.meta.env[key] || defaultValue;
};
```

#### API Configuration
```typescript
export const API_CONFIG = {
  BASE_URL: getEnvVariable('VITE_API_BASE_URL', '/api'),
  BACKEND_URL: getEnvVariable('VITE_API_BACKEND_URL', 'http://localhost:3000'),
  UPLOAD_URL: getEnvVariable('VITE_UPLOAD_URL', '/upload/file'),
  FILE_CONTENT_URL: getEnvVariable('VITE_FILE_CONTENT_URL', '/api/files/content'),
  ENABLE_LOGS: getEnvVariable('VITE_ENABLE_API_LOGS', 'false') === 'true',
};
```

#### SIP Configuration
```typescript
export const SIP_CONFIG = {
  // SIP Server
  SERVER_URL: getEnvVariable('VITE_SIP_SERVER_URL', '***************:5090'),
  WEBSOCKET_URL: getEnvVariable('VITE_SIP_WEBSOCKET_URL', 'ws://***************:8088/ws'),

  // Tài khoản mặc định (cho CSKH/Admin)
  DEFAULT_USER: getEnvVariable('VITE_SIP_DEFAULT_USER', 'cskh-1'),
  DEFAULT_PASSWORD: getEnvVariable('VITE_SIP_DEFAULT_PASSWORD', 'abc123@1'),

  // Tài khoản cho khách hàng (shop/buyer)
  CUSTOMER_USER: getEnvVariable('VITE_SIP_CUSTOMER_USER', 'kh-1'),
  CUSTOMER_PASSWORD: getEnvVariable('VITE_SIP_CUSTOMER_PASSWORD', 'abc123'),

  // Extensions được định nghĩa sẵn
  EXTENSIONS: {
    E1: '1',
    E2: '2',
    DEMO: '6000',
    JACK: 'jack',
    RECORDING: '6001'
  },

  // Cấu hình server bổ sung
  MAIN_SERVER: getEnvVariable('VITE_SIP_MAIN_SERVER', '**************:5060'),
  BACKUP_SERVER: getEnvVariable('VITE_SIP_BACKUP_SERVER', '***************:5060'),
  SIGNALING_PORT: getEnvVariable('VITE_SIP_SIGNALING_PORT', '5060'),
  RTP_PORT_RANGE: getEnvVariable('VITE_SIP_RTP_PORT_RANGE', '10000-65535'),
  FROM_PREFIX: getEnvVariable('VITE_SIP_FROM_PREFIX', '09'),
  TO_PREFIX: getEnvVariable('VITE_SIP_TO_PREFIX', '9'),

  // STUN servers cho WebRTC
  ICE_SERVERS: [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' }
  ]
};
```

#### App Configuration
```typescript
export const APP_CONFIG = {
  NAME: getEnvVariable('VITE_APP_NAME', 'GHVN - Hệ thống quản trị'),
  DESCRIPTION: getEnvVariable('VITE_APP_DESCRIPTION', 'GHVN - Hệ thống quản trị'),
  KEYWORDS: getEnvVariable('VITE_APP_KEYWORDS', 'GHVN, quản trị, helpdesk, customer service'),
  LOGO: getEnvVariable('VITE_APP_LOGO', '/assets/images/logo/logo_ghvn.png'),
};
```

#### Environment Configuration
```typescript
export const ENV_CONFIG = {
  IS_DEVELOPMENT: import.meta.env.DEV,
  IS_PRODUCTION: import.meta.env.PROD,
  MODE: import.meta.env.MODE,
};
```

### Tài khoản SIP có sẵn

#### Tài khoản CSKH/Admin
- **cskh-1**: Mật khẩu `abc123@1` (Dành cho nhân viên CSKH và Admin)

#### Tài khoản khách hàng
- **kh-1**: Mật khẩu `abc123` (Dành cho Shop và Buyer)

#### Số máy chăm sóc khách hàng
- **1001**: CSKH 1 (Hiển thị cho Shop/Buyer)
- **1002**: CSKH 2 (Hiển thị cho Shop/Buyer)

#### Tài khoản cũ (legacy)
- **e1**: Mật khẩu `Xproz2025` (Extension: 1)
- **e2**: Mật khẩu `Xproz2025` (Extension: 2)
- **webrtc1000**: Mật khẩu `abc123`

### Các số máy nhánh
- **1**: Extension e1
- **2**: Extension e2
- **6000**: demo-abouttotry
- **jack**: jack-vietnam-2
- **6001**: WebRTC call with recording

### Thông tin server thực tế

#### SIP Servers
- **Primary SIP Server**: `***************:5090`
- **WebSocket Development**: `ws://***************:8088/ws`
- **WebSocket Production**: `wss://sip.ghvn.vn/ws`
- **Main Server**: `**************:5060`
- **Backup Server**: `***************:5060`

#### Network Configuration
- **Signaling Port**: `5060`
- **RTP Port Range**: `10000-65535`
- **From Prefix**: `09`
- **To Prefix**: `9`

#### API Endpoints
- **Development Backend**: `http://localhost:3000`
- **Production Backend**: `https://api.ghvn.vn`
- **Chat Service**: `http://localhost:5055` (dev)
- **WebSocket Chat**: `ws://localhost:5055` (dev)

### Cách sử dụng cấu hình

#### Trong component
```typescript
import { SIP_CONFIG } from '../config/sipConfig';

// Sử dụng cấu hình
const server = SIP_CONFIG.WEBSOCKET_URL;
const username = SIP_CONFIG.DEFAULT_USER;
```

#### Override cấu hình local
Tạo file `.env.local` để override cấu hình (file này sẽ không được commit):
```bash
# Override cho development local
VITE_API_BACKEND_URL=http://*************:3000
VITE_SIP_SERVER_URL=*************:5090
VITE_SIP_WEBSOCKET_URL=ws://*************:8088/ws
VITE_SIP_DEFAULT_USER=test_user
VITE_SIP_DEFAULT_PASSWORD=test_password
VITE_ENABLE_API_LOGS=true
```

### Cấu hình WebRTC

#### ICE Servers (STUN/TURN)
```typescript
ICE_SERVERS: [
  { urls: 'stun:stun.l.google.com:19302' },
  { urls: 'stun:stun1.l.google.com:19302' }
]
```

#### Media Constraints
```typescript
// Cấu hình audio cho cuộc gọi
const mediaConstraints = {
  audio: {
    echoCancellation: true,
    noiseSuppression: true,
    autoGainControl: true
  },
  video: false
};
```

### Cấu hình Proxy (Vite)

#### vite.config.ts
```typescript
server: {
  proxy: {
    '/api': {
      target: backendUrl,
      changeOrigin: true,
      secure: false,
      cookieDomainRewrite: { '*': '' }
    }
  }
}
```

### Troubleshooting cấu hình

#### Lỗi kết nối WebSocket
1. **Kiểm tra URL WebSocket (Cấu hình thực tế)**:
   ```bash
   # Development
   VITE_SIP_WEBSOCKET_URL=ws://***************:8088/ws

   # Production
   VITE_SIP_WEBSOCKET_URL=wss://sip.ghvn.vn/ws
   ```

2. **Kiểm tra firewall/proxy**:
   - Port 8088 phải được mở cho WebSocket
   - Port 5060 cho SIP signaling
   - Port range 10000-65535 cho RTP
   - Proxy phải hỗ trợ WebSocket upgrade

#### Lỗi xác thực SIP
1. **Kiểm tra credentials (Thực tế)**:
   ```bash
   VITE_SIP_DEFAULT_USER=e1
   VITE_SIP_DEFAULT_PASSWORD=Xproz2025
   VITE_SIP_SERVER_URL=***************:5090
   ```

2. **Kiểm tra domain**:
   ```typescript
   // Domain được tự động extract từ SERVER_URL
   const domain = SIP_CONFIG.SERVER_URL.split(':')[0]; // ***************
   const aor = `sip:${username}@${domain}`;
   ```

3. **Backup servers**:
   ```bash
   VITE_SIP_MAIN_SERVER=**************:5060
   VITE_SIP_BACKUP_SERVER=***************:5060
   ```

#### Lỗi WebRTC/Media
1. **HTTPS requirement**: WebRTC yêu cầu HTTPS trong production
2. **Microphone permission**: Cần cấp quyền truy cập microphone
3. **STUN server**: Cần STUN server để NAT traversal

### Monitoring và Debug

#### Enable logs
```bash
# Development
VITE_ENABLE_API_LOGS=true

# Production
VITE_ENABLE_API_LOGS=false
```

#### Console debugging
```typescript
// Trong sipService.ts
console.log('Connecting to SIP server with:', {
  server: SIP_CONFIG.WEBSOCKET_URL,
  aor: `sip:${username}@${domain}`,
  username,
  domain
});
```

## Công nghệ sử dụng

- **SIP.js**: Thư viện JavaScript cho giao thức SIP
- **WebRTC**: Công nghệ gọi điện trực tiếp trên trình duyệt
- **React Hooks**: Quản lý state và lifecycle
- **TypeScript**: Type safety và IntelliSense
- **Tailwind CSS**: Styling và responsive design

## Trạng thái cuộc gọi

```typescript
enum CallStatus {
    IDLE = 'idle',           // Sẵn sàng
    CONNECTING = 'connecting', // Đang kết nối
    CONNECTED = 'connected',   // Đang gọi
    DISCONNECTED = 'disconnected', // Đã kết thúc
    INCOMING = 'incoming',     // Cuộc gọi đến
    OUTGOING = 'outgoing',     // Đang gọi đi
    ERROR = 'error'           // Lỗi
}
```

## Hướng dẫn sử dụng

### 1. Cấu hình kết nối
1. Nhập username và password SIP
2. Nhấn "Kết nối" để thiết lập kết nối

### 2. Thực hiện cuộc gọi
1. Nhập số điện thoại vào ô "Số điện thoại"
2. Chọn "Gọi" (với mic) hoặc "Chỉ nghe" (không mic)
3. Đợi kết nối và sử dụng các nút điều khiển

### 3. Nhận cuộc gọi
1. Khi có cuộc gọi đến, hệ thống sẽ tự động phát chuông báo
2. Chọn "Trả lời" hoặc "Từ chối" để xử lý cuộc gọi
3. Có thể chọn "Chỉ nghe" nếu không muốn sử dụng microphone
4. Chuông báo sẽ tự động dừng khi trả lời hoặc từ chối cuộc gọi

## Xử lý lỗi

### Lỗi microphone
- **NotFoundError**: Không tìm thấy microphone
- **NotAllowedError**: Quyền truy cập bị từ chối
- **AbortError**: Microphone đang được sử dụng

### Lỗi kết nối SIP
- Lỗi WebSocket
- Lỗi xác thực
- Lỗi mạng hoặc tường lửa

## Tích hợp

Trang call-center được tích hợp vào hệ thống chính thông qua:
- **DefaultLayout**: Layout chung của ứng dụng
- **SEO**: Tối ưu hóa SEO với title và description
- **Routing**: Được định tuyến trong hệ thống route chính

## Bảo trì và phát triển

### Thêm tính năng mới
1. Cập nhật `sipService.ts` cho logic backend
2. Thêm UI components trong `CallInterface.tsx`
3. Cập nhật hook `useSipCall.ts` nếu cần
4. Cập nhật types và interfaces

### Debug
- Kiểm tra console logs cho thông tin chi tiết
- Sử dụng browser dev tools cho WebRTC debugging
- Kiểm tra network tab cho WebSocket connections

## Deployment

### Yêu cầu hệ thống

#### Client (Browser)
- **HTTPS**: Bắt buộc cho WebRTC trong production
- **WebSocket support**: Tất cả trình duyệt hiện đại
- **WebRTC support**: Chrome 23+, Firefox 22+, Safari 11+
- **Microphone permission**: Cần user consent

#### Server
- **SIP Server**: Asterisk, FreeSWITCH, hoặc tương tự
- **WebSocket Gateway**: Cho SIP over WebSocket
- **STUN/TURN Server**: Cho NAT traversal
- **SSL Certificate**: Cho WSS (WebSocket Secure)

### Build và Deploy

#### Build production
```bash
npm run build
```

#### Environment variables cho production
```bash
# .env.production (Cấu hình thực tế)
VITE_API_BACKEND_URL=https://api.ghvn.vn
VITE_SIP_SERVER_URL=***************:5090
VITE_SIP_WEBSOCKET_URL=wss://sip.ghvn.vn/ws
VITE_SIP_DEFAULT_USER=e1
VITE_SIP_DEFAULT_PASSWORD=Xproz2025
VITE_SIP_MAIN_SERVER=**************:5060
VITE_SIP_BACKUP_SERVER=***************:5060
VITE_ENABLE_API_LOGS=false
```

#### Nginx configuration
```nginx
# WebSocket proxy cho SIP
location /ws {
    proxy_pass http://sip-server:8088;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
}
```

## Security

### Bảo mật kết nối
- **WSS (WebSocket Secure)**: Mã hóa kết nối WebSocket
- **HTTPS**: Bắt buộc cho WebRTC
- **SIP Authentication**: Username/password cho SIP registration

### Bảo mật thông tin
- **Environment Variables**: Không commit credentials vào git
- **Token-based Auth**: Sử dụng token thay vì password cố định
- **Rate Limiting**: Giới hạn số cuộc gọi per user/IP

### Best Practices
```typescript
// Không hardcode credentials
const username = process.env.VITE_SIP_DEFAULT_USER;
const password = process.env.VITE_SIP_DEFAULT_PASSWORD;

// Validate input
if (!destination || !/^[0-9a-zA-Z]+$/.test(destination)) {
  throw new Error('Invalid destination');
}

// Cleanup resources
useEffect(() => {
  return () => {
    sipService.disconnect();
  };
}, []);
```

## Performance

### Optimization
- **Lazy Loading**: Load SIP.js chỉ khi cần
- **Connection Pooling**: Tái sử dụng kết nối SIP
- **Audio Optimization**: Echo cancellation, noise suppression
- **Memory Management**: Cleanup media streams

### Monitoring
```typescript
// Track call quality
const trackCallQuality = (stats: RTCStatsReport) => {
  // Monitor packet loss, jitter, latency
  console.log('Call quality metrics:', stats);
};
```

## Lưu ý quan trọng

### Yêu cầu kỹ thuật
- **HTTPS**: Bắt buộc cho WebRTC trong production
- **Microphone Permission**: Cần user consent từ trình duyệt
- **WebSocket Support**: Cần proxy/gateway hỗ trợ WebSocket upgrade
- **CORS Configuration**: Cấu hình CORS trên SIP server
- **Firewall**: Mở ports cho SIP (5060) và RTP (10000-65535)

### Browser Compatibility
- **Chrome**: 23+ (Full support)
- **Firefox**: 22+ (Full support)
- **Safari**: 11+ (Limited support)
- **Edge**: 79+ (Chromium-based)

### Network Requirements
- **Bandwidth**: Tối thiểu 64kbps cho audio
- **Latency**: < 150ms cho chất lượng tốt
- **NAT Traversal**: Cần STUN/TURN server
- **QoS**: Ưu tiên traffic RTP nếu có thể

## Tính năng Chuông báo Cuộc gọi 🔔

### Mô tả
Hệ thống tự động phát chuông báo âm thanh khi có cuộc gọi đến, giúp người dùng dễ dàng nhận biết và không bỏ lỡ cuộc gọi quan trọng.

### Đặc điểm
- **Âm thanh chuyên biệt cho từng loại cuộc gọi**:
  - **Chuông đến** (`ring-coming.mp3`): Âm thanh khi có cuộc gọi đến (âm lượng 80%)
  - **Chuông đi** (`ring-wait.mp3`): Âm thanh khi gọi ra và đang chờ (âm lượng 70%)
- **Phát liên tục**: Chuông sẽ phát liên tục cho đến khi người dùng trả lời hoặc từ chối
- **Tự động dừng**: Chuông tự động dừng khi:
  - Trả lời cuộc gọi
  - Từ chối cuộc gọi
  - Cuộc gọi bị ngắt từ phía người gọi
  - Ngắt kết nối SIP
- **Hiển thị trực quan**: Icon chuông với animation bounce và text "Đang phát chuông..."
- **Điều chỉnh âm lượng**: Có thể điều chỉnh âm lượng chuông cho từng loại

### Công nghệ sử dụng
- **HTML5 Audio**: Phát file âm thanh với khả năng loop và điều khiển âm lượng
- **File âm thanh MP3**: Sử dụng file âm thanh thực tế cho chất lượng tốt nhất
- **Tự động phát/dừng**: Tích hợp với SIP service để tự động điều khiển

### Cách sử dụng
```typescript
import { ringToneService } from '@/pages/call-center/services/ringToneService';

// Phát chuông cho cuộc gọi đến (ring-coming.mp3)
ringToneService.startIncomingRing();

// Phát chuông cho cuộc gọi đi (ring-wait.mp3)
ringToneService.startOutgoingRing();

// Phát chuông mặc định (tương đương startIncomingRing)
ringToneService.startRinging();

// Dừng tất cả chuông
ringToneService.stopRinging();

// Kiểm tra trạng thái
const isRinging = ringToneService.isCurrentlyRinging;

// Điều chỉnh âm lượng (0.0 - 1.0)
ringToneService.setVolume(0.5);
```

### Tích hợp với SIP Service
Tính năng chuông báo được tích hợp tự động vào `sipService.ts`:
- **Cuộc gọi đến**: Tự động phát `ring-coming.mp3` khi nhận cuộc gọi đến (`CallStatus.INCOMING`)
- **Cuộc gọi đi**: Tự động phát `ring-wait.mp3` khi thực hiện cuộc gọi đi (`CallStatus.OUTGOING`)
- Tự động dừng chuông khi trả lời (`onCallAnswered`)
- Tự động dừng chuông khi từ chối (`rejectCall`)
- Tự động dừng chuông khi cuộc gọi kết thúc (`onCallHangup`)

### File âm thanh
Hệ thống sử dụng 2 file âm thanh chuyên biệt:

#### `/public/assets/audio/ring-coming.mp3`
- **Mục đích**: Chuông báo cho người nghe khi có cuộc gọi đến
- **Khi phát**: Khi trạng thái cuộc gọi là `CallStatus.INCOMING`
- **Đặc điểm**: Âm lượng cao (80%), loop liên tục
- **Dừng khi**: Trả lời, từ chối, hoặc cuộc gọi kết thúc

#### `/public/assets/audio/ring-wait.mp3`
- **Mục đích**: Chuông chờ cho người gọi khi thực hiện cuộc gọi đi
- **Khi phát**: Khi trạng thái cuộc gọi là `CallStatus.OUTGOING`
- **Đặc điểm**: Âm lượng vừa (70%), loop liên tục
- **Dừng khi**: Người nhận trả lời, từ chối, hoặc cuộc gọi kết thúc

### Browser Support
- **Chrome**: Full support
- **Firefox**: Full support
- **Safari**: Full support (iOS 13.4+)
- **Edge**: Full support
