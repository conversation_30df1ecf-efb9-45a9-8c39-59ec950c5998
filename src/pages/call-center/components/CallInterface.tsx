import React, { useState, useEffect, useCallback } from 'react';
import { useSipCall } from '../hooks/useSipCall';
import { useAudioRecording } from '../hooks/useAudioRecording';
import { CallStatus } from '../services/sipService';
import { SIP_CONFIG } from '../config/sipConfig';
import { aiService } from '../services/aiService';
import { callHistoryService } from '../services/callHistoryService';
import CallHistory from './CallHistory';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardFooter
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  PhoneCall,
  Phone,
  PhoneIncoming,
  PhoneOff,
  PhoneOutgoing,
  Mic,
  MicOff,
  Disc,
  RefreshCw,
  Clock,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Info,
  Bot,
  History,
  Settings,
  Volume2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { CallRecord, AIMode, CALL_STATUS_LABELS, formatCallDuration } from '../types/callHistory';

interface CallInterfaceProps {
    username?: string;
    password?: string;
    enableAI?: boolean;
    aiAutoAnswerDelay?: number;
    showCallHistory?: boolean;
}

const CallInterface: React.FC<CallInterfaceProps> = ({
    username = SIP_CONFIG.DEFAULT_USER,
    password = SIP_CONFIG.DEFAULT_PASSWORD,
    enableAI = true,
    aiAutoAnswerDelay = 10,
    showCallHistory = true
}) => {
    const [destination, setDestination] = useState('');
    const [callDuration, setCallDuration] = useState(0);
    const [timerInterval, setTimerInterval] = useState<NodeJS.Timeout | null>(null);
    const [currentCallId, setCurrentCallId] = useState<string | null>(null);
    const [aiMode, setAiMode] = useState<AIMode>({
        enabled: enableAI,
        autoAnswerDelay: aiAutoAnswerDelay,
        isActive: false
    });
    const [selectedCall, setSelectedCall] = useState<CallRecord | null>(null);

    const {
        callState,
        isInitialized,
        isMuted,
        isRinging,
        initialize,
        makeCall,
        answerCall,
        rejectCall,
        hangupCall,
        startRecording,
        stopRecording,
        toggleMute
    } = useSipCall({ autoConnect: false });

    // Audio recording hook
    const {
        isRecording,
        recordingDuration,
        startRecording: startAudioRecording,
        stopRecording: stopAudioRecording,
        saveRecording,
        formatDuration
    } = useAudioRecording({
        onRecordingComplete: handleRecordingComplete,
        onRecordingError: (error) => console.error('Recording error:', error),
        maxDuration: 3600 // 1 hour
    });

    // Handle recording completion
    async function handleRecordingComplete(audioBlob: Blob, duration: number) {
        if (!currentCallId) return;

        try {
            await callHistoryService.endCall({
                callId: currentCallId,
                audioBlob,
                callStatus: 'completed',
                duration,
                aiHandled: aiMode.isActive,
                aiResponse: aiMode.currentResponse
            });
            console.log('Call recording saved successfully');
        } catch (error) {
            console.error('Failed to save call recording:', error);
        }
    }

    // Start call and recording
    const startCallSession = useCallback(async (
        callerNumber: string,
        receiverNumber: string,
        callType: 'incoming' | 'outgoing'
    ) => {
        try {
            const response = await callHistoryService.startCall({
                sipSessionId: `sip_${Date.now()}`,
                callerNumber,
                receiverNumber,
                callType,
                agentId: username
            });

            setCurrentCallId(response.callId);
            console.log('Call session started:', response.callId);
            return response.callId;
        } catch (error) {
            console.error('Failed to start call session:', error);
            return null;
        }
    }, [username]);

    // Handle AI auto-answer
    const handleAIAutoAnswer = useCallback(async (transcript?: string) => {
        if (!enableAI || !currentCallId || !callState.remoteIdentity) return;

        try {
            setAiMode(prev => ({ ...prev, isActive: true }));

            const response = await aiService.handleAutoAnswer({
                callId: currentCallId,
                audioTranscript: transcript || 'Cuộc gọi đến từ khách hàng',
                customerInfo: {
                    phone: callState.remoteIdentity,
                    name: callState.remoteIdentity
                }
            });

            if (response.success && response.response) {
                setAiMode(prev => ({
                    ...prev,
                    currentResponse: response.response,
                    confidence: response.confidence
                }));

                // Play AI response audio if available
                if (response.audioUrl) {
                    const audio = new Audio(response.audioUrl);
                    audio.play().catch(err => console.error('Failed to play AI audio:', err));
                }
            } else {
                console.warn('AI auto-answer failed:', response.error);
                setAiMode(prev => ({ ...prev, isActive: false }));
            }
        } catch (error) {
            console.error('AI auto-answer error:', error);
            setAiMode(prev => ({ ...prev, isActive: false }));
        }
    }, [enableAI, currentCallId, callState.remoteIdentity]);

    // Xử lý khi component mount
    useEffect(() => {
        // Khởi tạo SIP service
        initialize(username, password);

        // Cleanup khi component unmount
        return () => {
            if (timerInterval) {
                clearInterval(timerInterval);
            }
        };
    }, [initialize, username, password]);

    // Auto-start recording when call connects
    useEffect(() => {
        if (callState.status === CallStatus.CONNECTED && !isRecording) {
            startAudioRecording();
        }
    }, [callState.status, isRecording, startAudioRecording]);

    // Auto-stop recording when call ends
    useEffect(() => {
        if ((callState.status === CallStatus.IDLE || callState.status === CallStatus.DISCONNECTED) && isRecording) {
            stopAudioRecording();
            setCurrentCallId(null);
            setAiMode(prev => ({ ...prev, isActive: false, currentResponse: undefined }));
        }
    }, [callState.status, isRecording, stopAudioRecording]);

    // Handle AI auto-answer for incoming calls
    useEffect(() => {
        if (callState.status === CallStatus.INCOMING && aiMode.enabled && !aiMode.isActive) {
            const timer = setTimeout(() => {
                handleAIAutoAnswer();
            }, aiMode.autoAnswerDelay * 1000);

            return () => clearTimeout(timer);
        }
    }, [callState.status, aiMode.enabled, aiMode.isActive, aiMode.autoAnswerDelay, handleAIAutoAnswer]);

    // Xử lý timer cho thời gian cuộc gọi
    useEffect(() => {
        if (callState.status === CallStatus.CONNECTED) {
            // Bắt đầu đếm thời gian
            const interval = setInterval(() => {
                setCallDuration(prev => prev + 1);
            }, 1000);

            setTimerInterval(interval);

            return () => {
                clearInterval(interval);
            };
        } else if (callState.status === CallStatus.DISCONNECTED || callState.status === CallStatus.IDLE) {
            // Dừng đếm thời gian
            if (timerInterval) {
                clearInterval(timerInterval);
                setTimerInterval(null);
            }

            // Reset thời gian nếu trạng thái là IDLE
            if (callState.status === CallStatus.IDLE) {
                setCallDuration(0);
            }
        }
    }, [callState.status]);

    // Format thời gian cuộc gọi (local function)
    const formatCallTime = (seconds: number): string => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    // Xử lý khi nhấn nút gọi
    const handleCall = async (options?: { skipMediaAccess?: boolean }) => {
        if (!destination) return;

        try {
            // Start call session
            const callId = await startCallSession(username, destination, 'outgoing');
            if (callId) {
                await makeCall(destination, options);
            }
        } catch (error) {
            console.error('Failed to make call', error);
        }
    };

    // Xử lý khi nhấn nút trả lời
    const handleAnswer = async (options?: { skipMediaAccess?: boolean }) => {
        try {
            // Start call session for incoming call
            if (callState.remoteIdentity) {
                await startCallSession(callState.remoteIdentity, username, 'incoming');
            }
            await answerCall(options);
        } catch (error) {
            console.error('Failed to answer call', error);
        }
    };

    // Xử lý khi nhấn nút từ chối
    const handleReject = async () => {
        try {
            await rejectCall();
        } catch (error) {
            console.error('Failed to reject call', error);
        }
    };

    // Xử lý khi nhấn nút kết thúc cuộc gọi
    const handleHangup = async () => {
        try {
            await hangupCall();
        } catch (error) {
            console.error('Failed to hangup call', error);
        }
    };

    // Xử lý khi nhấn nút ghi âm
    const handleRecording = () => {
        if (callState.recording) {
            stopRecording();
        } else {
            startRecording();
        }
    };

    // Xử lý khi nhấn nút tắt/bật microphone
    const handleToggleMute = () => {
        toggleMute();
    };

    // Render trạng thái cuộc gọi
    const renderCallStatus = () => {
        switch (callState.status) {
            case CallStatus.IDLE:
                return <Badge variant="secondary" className="flex items-center gap-1">
                    <CheckCircle2 className="h-3 w-3" />
                    Sẵn sàng
                </Badge>;
            case CallStatus.CONNECTING:
                return <Badge variant="secondary" className="flex items-center gap-1 bg-blue-500">
                    <RefreshCw className="h-3 w-3 animate-spin" />
                    Đang kết nối...
                </Badge>;
            case CallStatus.CONNECTED:
                return <Badge variant="default" className="flex items-center gap-1 bg-green-500">
                    <Phone className="h-3 w-3" />
                    Đang gọi
                </Badge>;
            case CallStatus.DISCONNECTED:
                return <Badge variant="outline" className="flex items-center gap-1 text-orange-500 border-orange-500">
                    <PhoneOff className="h-3 w-3" />
                    Đã kết thúc
                </Badge>;
            case CallStatus.INCOMING:
                return <Badge variant="default" className="flex items-center gap-1 bg-blue-500">
                    <PhoneIncoming className="h-3 w-3" />
                    Cuộc gọi đến
                </Badge>;
            case CallStatus.OUTGOING:
                return <Badge variant="default" className="flex items-center gap-1 bg-blue-500">
                    <PhoneOutgoing className="h-3 w-3" />
                    Đang gọi đi...
                </Badge>;
            case CallStatus.ERROR:
                return <Badge variant="destructive" className="flex items-center gap-1">
                    <XCircle className="h-3 w-3" />
                    Lỗi
                </Badge>;
            default:
                return null;
        }
    };

    return (
        <div className="space-y-6">
            <Card className="shadow-sm">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-xl font-bold flex items-center gap-2">
                        <PhoneCall className="h-5 w-5 text-primary" />
                        Giao diện gọi điện
                    </CardTitle>
                    <div className="flex items-center gap-2">
                        {renderCallStatus()}
                        {isInitialized ? (
                            <Badge variant="outline" className="flex items-center gap-1 text-green-500 border-green-500">
                                <CheckCircle2 className="h-3 w-3" />
                                Đã kết nối SIP
                            </Badge>
                        ) : (
                            <Badge variant="outline" className="flex items-center gap-1 text-red-500 border-red-500">
                                <XCircle className="h-3 w-3" />
                                Chưa kết nối SIP
                            </Badge>
                        )}
                        {aiMode.enabled && (
                            <Badge variant="outline" className="flex items-center gap-1 text-blue-500 border-blue-500">
                                <Bot className="h-3 w-3" />
                                AI {aiMode.isActive ? 'Hoạt động' : 'Sẵn sàng'}
                            </Badge>
                        )}
                    </div>
                </CardHeader>

                <CardContent className="pt-4 space-y-4">
                    {/* AI Mode Indicator */}
                    {aiMode.isActive && (
                        <Alert className="bg-blue-50 border-blue-200">
                            <Bot className="h-4 w-4 text-blue-500" />
                            <AlertTitle className="text-blue-700 flex items-center justify-between">
                                <span>🤖 AI đang xử lý cuộc gọi</span>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setAiMode(prev => ({ ...prev, isActive: false }))}
                                    className="text-blue-600 border-blue-300 hover:bg-blue-100"
                                >
                                    Chuyển sang thủ công
                                </Button>
                            </AlertTitle>
                            {aiMode.currentResponse && (
                                <AlertDescription className="mt-2">
                                    <div className="bg-white rounded p-2 border">
                                        <strong>AI Response:</strong>
                                        <p className="mt-1">{aiMode.currentResponse}</p>
                                        {aiMode.confidence && (
                                            <p className="text-xs text-muted-foreground mt-1">
                                                Độ tin cậy: {Math.round(aiMode.confidence * 100)}%
                                            </p>
                                        )}
                                    </div>
                                </AlertDescription>
                            )}
                        </Alert>
                    )}

                    {/* Recording Status */}
                    {isRecording && (
                        <Alert className="bg-red-50 border-red-200">
                            <Disc className="h-4 w-4 text-red-500 animate-pulse" />
                            <AlertTitle className="text-red-700 flex items-center justify-between">
                                <span>🔴 Đang ghi âm cuộc gọi</span>
                                <span className="font-mono text-sm">{formatDuration()}</span>
                            </AlertTitle>
                        </Alert>
                    )}

                    {/* Thông tin cuộc gọi */}
                    {callState.status !== CallStatus.IDLE && (
                        <Alert className="bg-blue-50 border-blue-200">
                            <div className="flex justify-between items-center">
                                <div className="flex items-center gap-2">
                                    <Phone className="h-4 w-4 text-blue-500" />
                                    <AlertTitle className="text-blue-700">
                                        {callState.remoteIdentity || `Đang gọi ${destination}`}
                                    </AlertTitle>
                                </div>
                                <div className="flex items-center gap-1 text-blue-700">
                                    <Clock className="h-4 w-4" />
                                    <span className="font-mono">{formatDuration(callDuration)}</span>
                                </div>
                            </div>
                        </Alert>
                    )}

                {/* Form nhập số điện thoại */}
                {(callState.status === CallStatus.IDLE || callState.status === CallStatus.DISCONNECTED) && (
                    <div className="space-y-2">
                        <label htmlFor="destination" className="text-sm font-medium flex items-center gap-2">
                            <Phone className="h-4 w-4" />
                            Số điện thoại
                        </label>
                        <div className="flex gap-2">
                            <Input
                                id="destination"
                                value={destination}
                                onChange={(e) => setDestination(e.target.value)}
                                placeholder="Nhập số điện thoại"
                                className="flex-1"
                            />
                            <Button
                                onClick={() => handleCall()}
                                disabled={!isInitialized || !destination}
                                title="Gọi với microphone"
                            >
                                <PhoneOutgoing className="h-4 w-4 mr-2" />
                                Gọi
                            </Button>
                            <Button
                                variant="outline"
                                onClick={() => handleCall({ skipMediaAccess: true })}
                                disabled={!isInitialized || !destination}
                                title="Gọi mà không cần microphone (chỉ nghe)"
                            >
                                <Phone className="h-4 w-4 mr-2" />
                                Chỉ nghe
                            </Button>
                        </div>
                    </div>
                )}

                {/* Hiển thị gợi ý khi có lỗi microphone */}
                {callState.status === CallStatus.ERROR && callState.error?.includes('microphone') && (
                    <Alert variant="destructive" className="bg-red-50 border-red-200">
                        <AlertCircle className="h-4 w-4" />
                        <AlertTitle>Lỗi truy cập microphone</AlertTitle>
                        <AlertDescription>
                            Bạn có thể sử dụng nút "Chỉ nghe" nếu không có microphone hoặc không muốn sử dụng microphone.
                        </AlertDescription>
                    </Alert>
                )}

                {/* Nút điều khiển cuộc gọi đến */}
                {callState.status === CallStatus.INCOMING && (
                    <div className="space-y-4">
                        <Alert className="bg-blue-50 border-blue-200">
                            <PhoneIncoming className={`h-4 w-4 text-blue-500 ${isRinging ? 'animate-bounce' : ''}`} />
                            <AlertTitle className="text-blue-700">
                                {callState.remoteIdentity || 'Cuộc gọi đến từ khách hàng'}
                                {isRinging && (
                                    <span className="ml-2 text-sm font-normal text-blue-600 animate-pulse">
                                        🔔 Đang phát chuông...
                                    </span>
                                )}
                            </AlertTitle>
                        </Alert>
                        <div className="flex justify-center gap-2">
                            <Button
                                variant="default"
                                className="bg-green-500 hover:bg-green-600"
                                onClick={() => handleAnswer()}
                                title="Trả lời với microphone"
                            >
                                <PhoneIncoming className="h-4 w-4 mr-2" />
                                Trả lời
                            </Button>
                            <Button
                                variant="outline"
                                className="text-green-500 border-green-500 hover:bg-green-50"
                                onClick={() => handleAnswer({ skipMediaAccess: true })}
                                title="Trả lời mà không cần microphone (chỉ nghe)"
                            >
                                <Phone className="h-4 w-4 mr-2" />
                                Chỉ nghe
                            </Button>
                            <Button
                                variant="destructive"
                                onClick={handleReject}
                            >
                                <PhoneOff className="h-4 w-4 mr-2" />
                                Từ chối
                            </Button>
                        </div>
                    </div>
                )}

                {/* Nút điều khiển cuộc gọi đang diễn ra */}
                {(callState.status === CallStatus.CONNECTED ||
                  callState.status === CallStatus.OUTGOING) && (
                    <div className="flex justify-center gap-2">
                        <Button
                            variant="destructive"
                            onClick={handleHangup}
                        >
                            <PhoneOff className="h-4 w-4 mr-2" />
                            Kết thúc
                        </Button>
                        <Button
                            variant={isMuted ? "outline" : "secondary"}
                            className={isMuted ? "border-orange-500 text-orange-500 hover:bg-orange-50" : ""}
                            onClick={handleToggleMute}
                        >
                            {isMuted ? <MicOff className="h-4 w-4 mr-2" /> : <Mic className="h-4 w-4 mr-2" />}
                            {isMuted ? 'Bật mic' : 'Tắt mic'}
                        </Button>
                        <Button
                            variant={callState.recording ? "outline" : "secondary"}
                            className={callState.recording ? "border-red-500 text-red-500 hover:bg-red-50" : ""}
                            onClick={handleRecording}
                            disabled={callState.status !== CallStatus.CONNECTED}
                        >
                            <Disc className="h-4 w-4 mr-2" />
                            {callState.recording ? 'Dừng ghi âm' : 'Ghi âm'}
                        </Button>
                    </div>
                )}

                {/* Hiển thị lỗi nếu có */}
                {callState.status === CallStatus.ERROR && callState.error && (
                    <Alert variant="destructive" className="mt-4">
                        <div className="flex justify-between items-center w-full">
                            <div className="flex items-center gap-2">
                                <AlertCircle className="h-4 w-4" />
                                <AlertTitle>Lỗi: {callState.error}</AlertTitle>
                            </div>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => initialize(username, password)}
                                title="Thử kết nối lại"
                                className="border-white text-white hover:bg-red-600"
                            >
                                <RefreshCw className="h-3 w-3 mr-1" />
                                Thử lại
                            </Button>
                        </div>
                        <AlertDescription className="mt-2">
                            <p className="font-medium">Nguyên nhân có thể:</p>
                            <ul className="list-disc list-inside text-sm mt-1 space-y-1">
                                <li>Máy chủ SIP không hoạt động hoặc không thể truy cập</li>
                                <li>Thông tin đăng nhập không chính xác</li>
                                <li>Vấn đề về mạng hoặc tường lửa</li>
                            </ul>
                        </AlertDescription>
                    </Alert>
                )}
                </CardContent>
            </Card>

            {/* Call History and Settings */}
            {showCallHistory && (
                <Tabs defaultValue="history" className="w-full">
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="history" className="flex items-center gap-2">
                            <History className="h-4 w-4" />
                            Lịch sử cuộc gọi
                        </TabsTrigger>
                        <TabsTrigger value="settings" className="flex items-center gap-2">
                            <Settings className="h-4 w-4" />
                            Cài đặt AI
                        </TabsTrigger>
                    </TabsList>

                    <TabsContent value="history" className="mt-4">
                        <CallHistory
                            agentId={username}
                            limit={10}
                            onCallSelect={setSelectedCall}
                        />
                    </TabsContent>

                    <TabsContent value="settings" className="mt-4">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Bot className="h-5 w-5" />
                                    Cài đặt AI Auto-Answer
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <label className="text-sm font-medium">Bật AI Auto-Answer</label>
                                        <p className="text-xs text-muted-foreground">
                                            AI sẽ tự động trả lời cuộc gọi đến sau {aiMode.autoAnswerDelay} giây
                                        </p>
                                    </div>
                                    <Button
                                        variant={aiMode.enabled ? "default" : "outline"}
                                        size="sm"
                                        onClick={() => setAiMode(prev => ({ ...prev, enabled: !prev.enabled }))}
                                    >
                                        {aiMode.enabled ? 'Bật' : 'Tắt'}
                                    </Button>
                                </div>

                                {aiMode.enabled && (
                                    <div className="space-y-2">
                                        <label className="text-sm font-medium">
                                            Thời gian chờ (giây): {aiMode.autoAnswerDelay}
                                        </label>
                                        <input
                                            type="range"
                                            min="5"
                                            max="30"
                                            value={aiMode.autoAnswerDelay}
                                            onChange={(e) => setAiMode(prev => ({
                                                ...prev,
                                                autoAnswerDelay: parseInt(e.target.value)
                                            }))}
                                            className="w-full"
                                        />
                                        <div className="flex justify-between text-xs text-muted-foreground">
                                            <span>5s</span>
                                            <span>30s</span>
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            )}

            {/* Selected Call Details Modal */}
            {selectedCall && (
                <Card className="border-primary">
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <CardTitle className="flex items-center gap-2">
                                <Phone className="h-5 w-5" />
                                Chi tiết cuộc gọi
                            </CardTitle>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setSelectedCall(null)}
                            >
                                <XCircle className="h-4 w-4" />
                            </Button>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            <div className="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span className="font-medium">Số điện thoại:</span>
                                    <p>{selectedCall.callerNumber}</p>
                                </div>
                                <div>
                                    <span className="font-medium">Thời gian:</span>
                                    <p>{new Date(selectedCall.startTime).toLocaleString('vi-VN')}</p>
                                </div>
                                <div>
                                    <span className="font-medium">Thời lượng:</span>
                                    <p>{formatCallDuration(selectedCall.duration)}</p>
                                </div>
                                <div>
                                    <span className="font-medium">Trạng thái:</span>
                                    <p>{CALL_STATUS_LABELS[selectedCall.callStatus]}</p>
                                </div>
                            </div>

                            {selectedCall.aiHandled && selectedCall.aiResponse && (
                                <div className="bg-blue-50 border border-blue-200 rounded p-3">
                                    <div className="flex items-center gap-2 mb-2">
                                        <Bot className="h-4 w-4 text-blue-600" />
                                        <span className="font-medium text-blue-600">AI Response</span>
                                    </div>
                                    <p className="text-sm text-blue-800">{selectedCall.aiResponse}</p>
                                </div>
                            )}

                            {selectedCall.audioUrl && (
                                <div className="border rounded p-3">
                                    <div className="flex items-center justify-between mb-2">
                                        <span className="font-medium">Ghi âm cuộc gọi</span>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => {
                                                const audio = new Audio(selectedCall.audioUrl);
                                                audio.play();
                                            }}
                                        >
                                            <Volume2 className="h-4 w-4 mr-2" />
                                            Phát
                                        </Button>
                                    </div>
                                    <audio controls src={selectedCall.audioUrl} className="w-full">
                                        Trình duyệt không hỗ trợ audio
                                    </audio>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>
            )}
        </div>
    );
};

export default CallInterface;
