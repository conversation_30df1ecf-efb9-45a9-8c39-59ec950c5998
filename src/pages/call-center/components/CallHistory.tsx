import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Card<PERSON>eader,
  Card<PERSON><PERSON>le,
  CardContent
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Phone,
  PhoneIncoming,
  PhoneOutgoing,
  Clock,
  Download,
  Search,
  Filter,
  Calendar,
  Volume2,
  Bot,
  User,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  CallRecord,
  CallHistoryParams,
  formatCallDuration,
  formatFileSize,
  getCallStatusColor,
  getCallTypeIcon,
  CALL_STATUS_LABELS,
  CALL_TYPE_LABELS
} from '../types/callHistory';
import { callHistoryService } from '../services/callHistoryService';

interface CallHistoryProps {
  agentId?: string;
  limit?: number;
  showFilters?: boolean;
  onCallSelect?: (call: CallRecord) => void;
}

const CallHistory: React.FC<CallHistoryProps> = ({
  agentId,
  limit = 20,
  showFilters = true,
  onCallSelect
}) => {
  const [calls, setCalls] = useState<CallRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<CallHistoryParams>({
    page: 1,
    limit,
    agentId
  });
  const [totalCalls, setTotalCalls] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);

  // Load call history
  const loadCallHistory = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const params = {
        ...filters,
        page: currentPage,
        limit
      };

      const response = searchQuery 
        ? await callHistoryService.searchCalls(searchQuery, params)
        : await callHistoryService.getCallHistory(params);

      setCalls(response.data);
      setTotalCalls(response.total);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Không thể tải lịch sử cuộc gọi');
      console.error('Failed to load call history:', err);
    } finally {
      setLoading(false);
    }
  }, [filters, currentPage, limit, searchQuery]);

  // Load data on mount and when dependencies change
  useEffect(() => {
    loadCallHistory();
  }, [loadCallHistory]);

  // Handle search
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    setCurrentPage(1);
  }, []);

  // Handle filter change
  const handleFilterChange = useCallback((newFilters: Partial<CallHistoryParams>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1);
  }, []);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Play audio recording
  const playRecording = useCallback((audioUrl: string) => {
    const audio = new Audio(audioUrl);
    audio.play().catch(err => {
      console.error('Failed to play audio:', err);
    });
  }, []);

  // Download recording
  const downloadRecording = useCallback(async (call: CallRecord) => {
    if (!call.audioUrl) return;

    try {
      const response = await fetch(call.audioUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `call_${call.callId}_${call.callerNumber}.wav`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Failed to download recording:', err);
    }
  }, []);

  // Render call item
  const renderCallItem = (call: CallRecord) => (
    <div
      key={call.callId}
      className={cn(
        "border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors",
        onCallSelect && "hover:border-primary"
      )}
      onClick={() => onCallSelect?.(call)}
    >
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-3 flex-1">
          {/* Call Type Icon */}
          <div className={cn(
            "p-2 rounded-full",
            call.callType === 'incoming' 
              ? "bg-blue-100 text-blue-600" 
              : "bg-green-100 text-green-600"
          )}>
            {call.callType === 'incoming' ? (
              <PhoneIncoming className="h-4 w-4" />
            ) : (
              <PhoneOutgoing className="h-4 w-4" />
            )}
          </div>

          {/* Call Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <span className="font-medium text-foreground">
                {call.customerInfo?.name || call.callerNumber}
              </span>
              {call.customerInfo?.name && (
                <span className="text-sm text-muted-foreground">
                  ({call.callerNumber})
                </span>
              )}
            </div>

            <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
              <span className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {new Date(call.startTime).toLocaleString('vi-VN')}
              </span>
              {call.duration && (
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {formatCallDuration(call.duration)}
                </span>
              )}
            </div>

            {/* AI Response */}
            {call.aiHandled && call.aiResponse && (
              <div className="bg-blue-50 border border-blue-200 rounded p-2 mt-2">
                <div className="flex items-center gap-1 mb-1">
                  <Bot className="h-3 w-3 text-blue-600" />
                  <span className="text-xs font-medium text-blue-600">AI Response</span>
                </div>
                <p className="text-xs text-blue-800 line-clamp-2">
                  {call.aiResponse}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Status and Actions */}
        <div className="flex flex-col items-end gap-2">
          <Badge className={cn("text-xs", getCallStatusColor(call.callStatus))}>
            {call.aiHandled && <Bot className="h-3 w-3 mr-1" />}
            {CALL_STATUS_LABELS[call.callStatus]}
          </Badge>

          {/* Audio Controls */}
          {call.audioUrl && (
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  playRecording(call.audioUrl!);
                }}
                title="Phát ghi âm"
              >
                <Volume2 className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  downloadRecording(call);
                }}
                title="Tải xuống"
              >
                <Download className="h-3 w-3" />
              </Button>
            </div>
          )}

          {call.audioSize && (
            <span className="text-xs text-muted-foreground">
              {formatFileSize(call.audioSize)}
            </span>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Phone className="h-5 w-5" />
            Lịch sử cuộc gọi
            {totalCalls > 0 && (
              <Badge variant="secondary">{totalCalls}</Badge>
            )}
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={loadCallHistory}
            disabled={loading}
          >
            <RefreshCw className={cn("h-4 w-4 mr-2", loading && "animate-spin")} />
            Làm mới
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Search and Filters */}
        {showFilters && (
          <div className="space-y-3">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Tìm kiếm theo số điện thoại, tên khách hàng..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Quick Filters */}
            <div className="flex flex-wrap gap-2">
              <Button
                variant={!filters.callType ? "default" : "outline"}
                size="sm"
                onClick={() => handleFilterChange({ callType: undefined })}
              >
                Tất cả
              </Button>
              <Button
                variant={filters.callType === 'incoming' ? "default" : "outline"}
                size="sm"
                onClick={() => handleFilterChange({ callType: 'incoming' })}
              >
                <PhoneIncoming className="h-3 w-3 mr-1" />
                Cuộc gọi đến
              </Button>
              <Button
                variant={filters.callType === 'outgoing' ? "default" : "outline"}
                size="sm"
                onClick={() => handleFilterChange({ callType: 'outgoing' })}
              >
                <PhoneOutgoing className="h-3 w-3 mr-1" />
                Cuộc gọi đi
              </Button>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">Đang tải...</span>
          </div>
        )}

        {/* Call List */}
        {!loading && calls.length > 0 && (
          <div className="space-y-3">
            {calls?.map(renderCallItem)}
          </div>
        )}

        {/* Empty State */}
        {!loading && calls.length === 0 && !error && (
          <div className="text-center py-8">
            <Phone className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              {searchQuery ? 'Không tìm thấy cuộc gọi nào' : 'Chưa có cuộc gọi nào'}
            </p>
          </div>
        )}

        {/* Pagination */}
        {totalCalls > limit && (
          <div className="flex items-center justify-between pt-4 border-t">
            <span className="text-sm text-muted-foreground">
              Hiển thị {(currentPage - 1) * limit + 1}-{Math.min(currentPage * limit, totalCalls)} của {totalCalls}
            </span>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Trước
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage * limit >= totalCalls}
              >
                Sau
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CallHistory;
