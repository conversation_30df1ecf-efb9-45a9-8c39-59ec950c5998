/**
 * Types và interfaces cho Call History và AI features
 */

export interface CallRecord {
  _id: string;
  callId: string;
  sipSessionId: string;
  callerNumber: string;
  receiverNumber: string;
  callType: 'incoming' | 'outgoing';
  callStatus: 'completed' | 'missed' | 'rejected' | 'ai_answered' | 'connecting' | 'ongoing';
  startTime: Date;
  endTime?: Date;
  duration?: number; // seconds
  audioUrl?: string;
  audioFileId?: string;
  audioSize?: number; // bytes
  aiHandled: boolean;
  aiTranscript?: string;
  aiResponse?: string;
  agentId?: string;
  customerInfo?: {
    name?: string;
    phone: string;
    customerId?: string;
  };
  metadata?: {
    callQuality?: 'good' | 'average' | 'poor';
    recordingQuality?: string;
    deviceInfo?: any;
    networkInfo?: any;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface CallHistoryParams {
  page?: number;
  limit?: number;
  agentId?: string;
  dateFrom?: string;
  dateTo?: string;
  callType?: 'incoming' | 'outgoing';
  callStatus?: string;
}

export interface CallHistoryResponse {
  data: CallRecord[];
  total: number;
  page: number;
  limit: number;
}

export interface AIConfiguration {
  _id: string;
  name: string;
  modelType: 'openai' | 'custom' | 'local';
  modelVersion: string;
  settings: {
    temperature: number;
    maxTokens: number;
    systemPrompt: string;
    enableAutoAnswer: boolean;
    autoAnswerDelay: number; // seconds
    confidenceThreshold: number;
  };
  active: boolean;
  trainingDataVersion: string;
  lastTrained?: Date;
  performance?: {
    accuracy: number;
    responseTime: number;
    customerSatisfaction: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface AIAutoAnswerRequest {
  callId: string;
  audioTranscript: string;
  customerInfo?: {
    name?: string;
    phone: string;
    customerId?: string;
  };
}

export interface AIAutoAnswerResponse {
  success: boolean;
  response?: string;
  audioUrl?: string;
  confidence?: number;
  error?: string;
  fallbackMessage?: string;
}

export interface StartCallRequest {
  sipSessionId: string;
  callerNumber: string;
  receiverNumber: string;
  callType: 'incoming' | 'outgoing';
  agentId?: string;
  customerInfo?: {
    name?: string;
    phone: string;
    customerId?: string;
  };
}

export interface StartCallResponse {
  callId: string;
  record: CallRecord;
}

export interface EndCallRequest {
  callId: string;
  audioBlob?: Blob;
  callStatus: 'completed' | 'rejected' | 'missed';
  duration?: number;
  aiHandled?: boolean;
  aiTranscript?: string;
  aiResponse?: string;
}

export interface EndCallResponse {
  success: boolean;
  record: CallRecord;
  audioUrl?: string;
}

export interface AudioRecordingState {
  isRecording: boolean;
  mediaRecorder: MediaRecorder | null;
  audioChunks: Blob[];
  recordingStartTime: Date | null;
  recordingDuration: number;
}

export interface AIMode {
  enabled: boolean;
  autoAnswerDelay: number;
  isActive: boolean;
  currentResponse?: string;
  confidence?: number;
}

// Utility types
export type CallStatusType = CallRecord['callStatus'];
export type CallTypeType = CallRecord['callType'];

// Constants
export const CALL_STATUS_LABELS: Record<CallStatusType, string> = {
  completed: 'Hoàn thành',
  missed: 'Nhỡ cuộc gọi',
  rejected: 'Từ chối',
  ai_answered: 'AI trả lời',
  connecting: 'Đang kết nối',
  ongoing: 'Đang gọi'
};

export const CALL_TYPE_LABELS: Record<CallTypeType, string> = {
  incoming: 'Cuộc gọi đến',
  outgoing: 'Cuộc gọi đi'
};

export const CALL_QUALITY_LABELS = {
  good: 'Tốt',
  average: 'Trung bình',
  poor: 'Kém'
};

// Helper functions
export const formatCallDuration = (seconds?: number): string => {
  if (!seconds) return '00:00';
  
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

export const formatFileSize = (bytes?: number): string => {
  if (!bytes) return '0 B';
  
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
};

export const getCallStatusColor = (status: CallStatusType): string => {
  switch (status) {
    case 'completed':
      return 'text-green-600 bg-green-50 border-green-200';
    case 'ai_answered':
      return 'text-blue-600 bg-blue-50 border-blue-200';
    case 'ongoing':
    case 'connecting':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    case 'missed':
    case 'rejected':
      return 'text-red-600 bg-red-50 border-red-200';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
};

export const getCallTypeIcon = (type: CallTypeType): string => {
  return type === 'incoming' ? '📞' : '📱';
};
