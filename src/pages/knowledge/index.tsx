import React, {useEffect, useState} from 'react';
import {But<PERSON>} from '@/components/ui/button';
import {Switch} from '@/components/ui/switch';
import {Input} from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {Badge} from '@/components/ui/badge.tsx'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {PaginationCustom, PaginationBase} from '@/components/custom/Pagination';
import {DefaultLayout} from "@/components/layout/DefaultLayout";
import {Plus, Trash2} from 'lucide-react';
import UploadDialog from './components/UploadDialog';
import {useKnowledge} from '@/hooks/useKnowledge';
import {SEO} from "@/components/SEO.tsx";
import {knowledgeService} from '@/services/KnowledgeService.ts'
import {enhancedToast} from "@/components/common/EnhancedToast.tsx";

interface FilterState {
  filename: string;
  isActive: boolean | null;
}

const statusTraining = {
  success: {
    title: "Đã huấn luyện",
    variant: "success_o"
  },
  training: {
    title: "Đang huấn luyện",
    variant: "warning_o"
  },
  error: {
    title: "Huấn luyện lỗi",
    variant: "error_o"
  },
}

const Knowledge = () => {
  const {documents, setDocuments, addMultipleDocuments, toggleStatus} = useKnowledge();
  const [pagination, setPagination] = useState<PaginationBase>({
    page: 1,
    limit: 10,
    totalPage: 0
  });
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [filters, setFilters] = useState<FilterState>({
    filename: '',
    isActive: null
  });

  useEffect(() => {
    getData().then()
  }, []);

  const getData = async (page = pagination.page, limit = pagination.limit, query = filters) => {
    const api = await knowledgeService.getAllKnowledge({
      ...query,
      pagination: true,
      page: page,
      limit: limit,
    })

    if (api) {
      setDocuments(api.data)
      setPagination({
        page: api.currentPage,
        limit: pagination.limit,
        totalPage: api.totalPage
      })
    }
  }

  const onchangePagination = async (newPage) => {
    await getData(newPage).then()
  }

  const handleDelete = async (id: string) => {
    const api = await knowledgeService.deleteKnowledge(id);
    if(api?.success) {
      setDocuments(prev => prev.filter(doc => doc._id !== id));
      await getData()
      enhancedToast.success(`Xóa văn bản thành công!`)
    }
  };

  const handleStatusToggle = async (id: string, currentStatus: boolean) => {
    await toggleStatus(id, currentStatus);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
  };

  const handleFilter = async () => {
    await getData();
  }

  const handleResetFilters = async () => {
    setFilters({filename: '', isActive: null});
    await getData(pagination.page, pagination.limit, {filename: '', isActive: null});
  };


  return (
    <DefaultLayout>
      <SEO title="Chat với khách hàng" description="Tương tác với khách hàng qua hệ thống chat thời gian thực"/>
      <div className="space-y-6">
        <div className="mx-auto">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Tài liệu huấn luyện</h1>
              <p className="text-muted-foreground">
                Quản lý các văn bản được tải lên được chatbot sử dụng.
              </p>
            </div>

            <Button onClick={() => setUploadDialogOpen(true)}>
              <Plus className="mr-1 h-4 w-4"/>
              Thêm mới
            </Button>
          </div>

          {/* Filter Section */}
          <div className="bg-card rounded-lg border p-4 mb-6">
            <div className="flex flex-col sm:flex-row gap-4 items-end">

              <div className="w-full sm:w-96">
                <label className="text-sm font-medium mb-2 block">Tên văn bản</label>
                <div className="flex gap-2">
                  <Input
                    placeholder="Nhập tên văn bản..."
                    className="flex-1"
                    value={filters.filename}
                    onChange={(e) => {
                      setFilters(prevState => {
                        return {
                          ...prevState,
                          filename: e.target.value
                        }
                      })
                    }}
                  />
                </div>
              </div>

              <div className="w-full sm:w-48">
                <label className="text-sm font-medium mb-2 block">Trạng thái</label>
                <Select
                  value={
                    filters.isActive === true ? 'active' :
                      filters.isActive === false ? 'inactive' : 'all'
                  }
                  onValueChange={(e) => {
                    const value = e === "active" ? true : e === "inactive" ? false : null;
                    setFilters(prevState => {
                      return {
                        ...prevState,
                        isActive: value
                      }
                    })
                  }}

                >
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn trạng thái"/>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tất cả</SelectItem>
                    <SelectItem value="active">Được sử dụng</SelectItem>
                    <SelectItem value="inactive">Không sử dụng</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="w-full sm:w-48 flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => handleFilter()}
                >
                  Lọc
                </Button>

                <Button
                  variant="outline"
                  onClick={() => handleResetFilters()}
                >
                  Đặt lại
                </Button>
              </div>


            </div>
          </div>

          <div className="bg-card rounded-lg border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tên văn bản</TableHead>
                  <TableHead>Ngày tải lên</TableHead>
                  <TableHead>Được sử dụng</TableHead>
                  <TableHead>Trạng thái huấn luyện</TableHead>
                  <TableHead className="text-right">Thao tác</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {documents.map((document) => (
                  <TableRow key={document._id}>
                    <TableCell className="font-medium">
                      {document.filename}
                    </TableCell>
                    <TableCell className="w-[200px]">{formatDate(document.createdAt)}</TableCell>
                    <TableCell className="w-[220px]">
                      <div className="flex items-center text-center space-x-2">
                        <Switch
                          disabled={document.status !== 'success'}
                          checked={document.isActive}
                          onCheckedChange={(checked) => handleStatusToggle(document._id, checked)}
                        />
                      </div>
                    </TableCell>
                    <TableCell className="w-[220px]">
                      <Badge variant={statusTraining[document.status].variant}>{statusTraining[document.status].title}</Badge>
                    </TableCell>
                    <TableCell className="text-center w-[90px]">
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="destructive" size="xs">
                            <Trash2 className="h-4 w-4"/>
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Xác nhận xóa</AlertDialogTitle>
                            <AlertDialogDescription>
                              Bạn có chắc chắn muốn xóa văn bản "{document.filename}"?
                              <br />
                              Hành động này không thể hoàn tác.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Hủy</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDelete(document._id)}
                              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                            >
                              Xóa
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {pagination.totalPage > 1 && (
              <div className="flex justify-center mt-6">
                <PaginationCustom
                    onchangePagination={onchangePagination}
                    pagination={pagination}
                />
              </div>
          )}

          <UploadDialog
            open={uploadDialogOpen}
            onOpenChange={setUploadDialogOpen}
            onUpload={addMultipleDocuments}
          />
        </div>
      </div>
    </DefaultLayout>
  );
};

export default Knowledge;