import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {enhancedToast} from "@/components/common/EnhancedToast.tsx";

interface UploadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUpload: (files: File[]) => void;
}

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

const UploadDialog: React.FC<UploadDialogProps> = ({
                                                     open,
                                                     onOpenChange,
                                                     onUpload,
                                                   }) => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

  useEffect(() => {
    if (!open) {
      setSelectedFiles([]);
    }
  }, [open]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const validFiles: File[] = [];
      const rejectedFiles: string[] = [];

      Array.from(files).forEach((file) => {
        if (file.size <= MAX_FILE_SIZE) {
          validFiles.push(file);
        } else {
          rejectedFiles.push(file.name);
        }
      });

      if (rejectedFiles.length > 0) {
        enhancedToast.error(`Các file vượt quá 10MB đã bị loại: \n${rejectedFiles.join('\n')}`);
      }

      setSelectedFiles(validFiles);
    }
  };

  const handleUpload = () => {
    if (selectedFiles.length === 0) {
      enhancedToast.error(`Vui lòng chọn file để tải lên`)
      return;
    }

    onUpload(selectedFiles);
    setSelectedFiles([]);
    onOpenChange(false);
  };

  const handleCancel = () => {
    setSelectedFiles([]);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange} >
      <DialogContent className="sm:max-w-[425px] md:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Tải lên văn bản mới</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="file text-sm">Chọn tài liệu huấn luyện (có thể chọn nhiều file)</Label>
            <Input
              id="file"
              type="file"
              accept=".pdf,.doc,.docx,.txt,.md,.odt,.xlsx"
              onChange={handleFileChange}
              multiple
            />
            {selectedFiles.length > 0 && (
              <div className="text-sm text-muted-foreground">
                <p>Đã chọn {selectedFiles.length} file:</p>
                <ul className="list-disc list-inside max-h-32 overflow-y-auto">
                  {selectedFiles.map((file, index) => (
                    <li key={index}>{file.name}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            Hủy
          </Button>
          <Button onClick={handleUpload}>Upload</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default UploadDialog;