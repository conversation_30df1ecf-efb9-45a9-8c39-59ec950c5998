import React, {useState, useEffect} from 'react';
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from '@/components/ui/card';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {Label} from '@/components/ui/label';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/select';
import CodemirrorMarkdown from '@/components/custom/CodemirrorMarkdown';
import {Slider} from '@/components/ui/slider';
import {Settings, Key, Thermometer, MessageSquare, LoaderCircle} from 'lucide-react';
import {pick} from 'lodash';
import {chatbotService, ChatbotUpdate, Chatbot} from '@/services/OpenAIService.ts'
import {enhancedToast} from "@/components/common/EnhancedToast.tsx";

const OpenAI: React.FC = () => {
  const [chatbot, setChatbot] = useState<ChatbotUpdate | null>(null);
  const [chatbotId, setChatbotId] = useState<string | null>(null);
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    getData().then()
  }, [])

  const getData = async () => {
    const api: Chatbot = await chatbotService.getConfigChatbot()
    if (!api) return;
    setChatbot(pick(api, [
      "defaultPrompt",
      "openaiApiKey",
      "modelName",
      "temperature",
      "historyMessageCount"
    ]) as ChatbotUpdate);
    setChatbotId(api._id)
  }

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!chatbot.openaiApiKey.trim()) {
      newErrors.openaiApiKey = "OpenAI API Key là bắt buộc";
    }

    if (chatbot.historyMessageCount < 0 || chatbot.historyMessageCount > 10) {
      newErrors.historyMessageCount = "Số tin nhắn lịch sử phải từ 1 đến 20";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      enhancedToast.error("Vui lòng kiểm tra và sửa lỗi.");
      return;
    }

    setIsLoading(true);

    const api = await chatbotService.updateConfigChatbot(chatbotId, chatbot)
    if (api) {
      setErrors({})
      enhancedToast.success("Tất cả thay đổi đã được áp dụng.");
    }
    setIsLoading(false);
  };

  const handleInputChange = (field: keyof ChatbotUpdate, value: string | number) => {
    setChatbot(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const openaiModels = [
    {value: "gpt-4", label: "GPT-4"},
    {value: "gpt-4-turbo", label: "GPT-4 Turbo"},
    {value: "gpt-3.5-turbo", label: "GPT-3.5 Turbo"},
    {value: "gpt-4o", label: "GPT-4o"},
    {value: "gpt-4o-mini", label: "GPT-4o Mini"},
  ];

  return (<div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Cấu hình Chatbot OpenAI</h1>
        <p className="text-muted-foreground">
          Quản lý các thông số và cài đặt cho chatbot của bạn
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2">

        <div className="grid gap-4 col-span-1 md:col-span-2 xl:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Cấu hình API
              </CardTitle>
              <CardDescription>
                Thiết lập kết nối với OpenAI API
              </CardDescription>
            </CardHeader>

            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <div className="flex items-start gap-3">
                  <Key className="h-5 w-5 mt-1 text-green-600"/>
                  <div className="flex-1 space-y-2">
                    <Label htmlFor="openai_api_key">OpenAI API Key</Label>
                    <p className="text-xs text-muted-foreground">
                      API key của bạn từ OpenAI platform
                    </p>
                    <Input
                      id="openai_api_key"
                      type="password"
                      placeholder="sk-..."
                      value={chatbot?.openaiApiKey}
                      onChange={(e) => handleInputChange('openaiApiKey', e.target.value)}
                      className={`font-mono ${errors.openaiApiKey ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`}
                    />
                    {errors.openaiApiKey && (
                      <p className="text-sm text-red-600 mt-1">{errors.openaiApiKey}</p>
                    )}
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <Settings className="h-5 w-5 mt-1 text-purple-600"/>
                  <div className="flex-1 space-y-2">
                    <Label htmlFor="model_name">Model</Label>
                    <p className="text-xs text-muted-foreground">
                      Model AI sẽ được sử dụng cho chatbot
                    </p>
                    <Select
                      value={chatbot?.modelName}
                      onValueChange={(value) => handleInputChange('modelName', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn model"/>
                      </SelectTrigger>
                      <SelectContent>
                        {openaiModels.map((model) => (
                          <SelectItem key={model.value} value={model.value}>
                            {model.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                  </div>
                </div>
              </div>
            </CardContent>
          </Card>


          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Thông số hoạt động
              </CardTitle>
              <CardDescription>
                Điều chỉnh cách thức hoạt động của chatbot
              </CardDescription>
            </CardHeader>

            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <div className="flex items-start gap-3">
                  <Thermometer className="h-5 w-5 mt-1 text-orange-600"/>
                  <div className="flex-1 space-y-4">
                    <div className="space-y-2">
                      <Label>Temperature: {chatbot?.temperature}</Label>
                      <p className="text-xs text-muted-foreground">
                        Độ sáng tạo trong câu trả lời (0.0 - 1.0)
                      </p>
                    </div>
                    <Slider
                      value={[chatbot?.temperature]}
                      onValueChange={(value) => handleInputChange('temperature', value[0])}
                      max={1}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Chính xác (0.0)</span>
                      <span>Sáng tạo (1.0)</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <MessageSquare className="h-5 w-5 mt-1 text-blue-600"/>
                  <div className="flex-1 space-y-2">
                    <Label htmlFor="history_count">Số tin nhắn lịch sử</Label>
                    <p className="text-xs text-muted-foreground">
                      Số lượng tin nhắn trước đó mà chatbot sẽ nhớ
                    </p>
                    <Input
                      id="history_count"
                      type="number"
                      min="0"
                      max="10"
                      value={chatbot?.historyMessageCount}
                      onChange={(e) => handleInputChange('historyMessageCount', parseInt(e.target.value) || 1)}
                      className={errors.historyMessageCount ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}
                    />
                    {errors.historyMessageCount && (
                      <p className="text-sm text-red-600 mt-1">{errors.historyMessageCount}</p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="col-span-1 md:col-span-2 xl:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Prompt mặc định
              </CardTitle>
              <CardDescription>
                Thiết lập hướng dẫn cơ bản cho chatbot
              </CardDescription>
            </CardHeader>

            <CardContent>
              <div className="flex items-start gap-3">
                <MessageSquare className="h-5 w-5 mt-1 text-blue-600"/>
                <div className="flex-1 space-y-2">
                  <Label htmlFor="default_prompt">System Prompt</Label>
                  <p className="text-xs text-muted-foreground">
                    Đây là hướng dẫn cơ bản mà chatbot sẽ tuân theo trong mọi cuộc hội thoại
                  </p>
                  <CodemirrorMarkdown
                    placeholder="Nhập hướng dẫn cho chatbot..."
                    value={chatbot?.defaultPrompt}
                    minHeight={300}
                    maxHeight={300}
                    onChange={(value) => handleInputChange('defaultPrompt', value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

        </div>
      </div>
      <div className="flex justify-end pt-6">
        <Button
          onClick={handleSave}
          disabled={isLoading}
        >
          {isLoading && <LoaderCircle className="h-4 w-4 animate-spin slow-spin"/>}
          Cập nhật
        </Button>
      </div>
    </div>
  );
};

export default OpenAI;