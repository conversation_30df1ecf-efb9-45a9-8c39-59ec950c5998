import React, {useState, useEffect} from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
} from '@/components/ui/dialog';
import {Button} from '@/components/ui/button';
import {topicService, TopicBody} from '@/services/TopicService.ts'
import {pick} from 'lodash';
import {Input} from '@/components/ui/input';
import {Label} from '@/components/ui/label';
import {LoaderCircle} from 'lucide-react';
import {enhancedToast} from "@/components/common/EnhancedToast.tsx";
import {Textarea} from "@/components/ui/textarea.tsx";
import CodemirrorMarkdown from '@/components/custom/CodemirrorMarkdown';

interface DialogProps {
  _id?: string;
  open: boolean;
  handleCancel: () => void;
  handleOk: (id: string, data: TopicBody) => void;
}

const DialogTopic: React.FC<DialogProps> = ({
                                              _id,
                                              open,
                                              handleCancel,
                                              handleOk,
                                            }) => {
  const [id, setId] = useState<string>(null)
  const [data, setData] = useState<TopicBody>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [errors, setErrors] = useState<{ [key: string]: string }>({})

  useEffect(() => {
    if (open && _id) {
      getOneData().then()
    } else {
      setId(null)
      setData(null)
    }
  }, [_id, open]);

  const getOneData = async () => {
    const api = await topicService.getTopic(_id)
    if (api) {
      setData(pick(api, [
        "title",
        "prompt",
        "description",
      ]) as TopicBody);
      setId(_id)
    }
  }

  const handleInputChange = (field: keyof TopicBody, value: string | number) => {
    setData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!data.title.trim()) {
      newErrors.title = "Tên chủ đề là bắt buộc";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      enhancedToast.error("Vui lòng kiểm tra và sửa lỗi.");
      return;
    }
    await handleOk(id, data)
    setIsLoading(false);
  };


  return (
    <Dialog open={open} onOpenChange={handleCancel}>
      <DialogContent className="sm:max-w-[500px] md:max-w-[700px]">
        <DialogHeader>
          <DialogTitle>{_id ? 'Cập nhật chủ đề' : 'Thêm mới chủ đề'}</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="flex items-start gap-3">
            <div className="flex-1 space-y-2">
              <Label htmlFor="title">Tên chủ đề</Label>
              <Input
                id="title"
                type="text"
                placeholder="Nhập tên chủ đề..."
                value={data?.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className={`font-mono ${errors.title ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`}
              />
              {errors.title && (
                <p className="text-sm text-red-600 mt-1">{errors.title}</p>
              )}
            </div>
          </div>

          <div className="flex items-start gap-3">
            <div className="flex-1 space-y-2">
              <Label htmlFor="description">Mô tả</Label>
              <Textarea
                id="description"
                placeholder="Nhập mô tả cho chủ đề..."
                value={data?.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className="min-h-[80px] resize-none"
              />
            </div>
          </div>

          <div className="flex items-start gap-3">
            <div className="flex-1 space-y-2">
              <Label htmlFor="prompt">Hướng dẫn chatbot</Label>
              <p className="text-xs text-muted-foreground">
                Đưa ra thông tin ngợi ý xử lý khi cuộc trò chuyện bắt đầu với chủ đề này
              </p>
              <CodemirrorMarkdown
                placeholder="Nhập hướng dẫn cho chatbot..."
                value={data?.prompt}
                maxHeight={200}
                onChange={(value) => handleInputChange('prompt', value)}
              />
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button
            disabled={isLoading}
            variant="outline"
            onClick={handleCancel}
          >
            Hủy
          </Button>
          <Button
            disabled={isLoading}
            onClick={handleSave}
          >
            {isLoading && <LoaderCircle className="h-4 w-4 animate-spin slow-spin"/>}
            {_id ? "Cập nhật" : "Thêm mới"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DialogTopic;