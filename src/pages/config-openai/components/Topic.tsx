import React, {useEffect, useState} from 'react';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {PaginationCustom, PaginationBase} from '@/components/custom/Pagination';
import {Plus, Trash2, SquarePen} from 'lucide-react';
import DialogTopic from './DialogTopic.tsx';
import {topicService, Topic} from '@/services/TopicService.ts'
import {enhancedToast} from "@/components/common/EnhancedToast.tsx";


interface FilterState {
    title: string;
}

const Topic = () => {
    const [id, setId] = useState<string>(null)
    const [topics, setTopics] = useState<Topic[]>([])
    const [dialogOpen, setDialogOpen] = useState(false);
    const [pagination, setPagination] = useState<PaginationBase>({
        page: 1,
        limit: 10,
        totalPage: 0
    });
    const [filters, setFilters] = useState<FilterState>({
        title: '',
    });

    useEffect(() => {
        getData().then()
    }, []);

    const getData = async (page = pagination.page, limit = pagination.limit, query = filters) => {
        const api = await topicService.getAllTopic({
            ...query,
            pagination: true,
            page: page,
            limit: limit,
        })

        if (api) {
            setTopics(api.data)
            setPagination({
                page: api.currentPage,
                limit: pagination.limit,
                totalPage: api.totalPage
            })
        }
    }

    const onchangePagination = async (newPage) => {
        await getData(newPage).then()
    }

    const handleDelete = async (id: string) => {
        const api = await topicService.deleteTopic(id);
        if (api?.success) {
            await getData()
            enhancedToast.success(`Xóa văn bản thành công!`)
        }
    };


    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('vi-VN');
    };

    const handleFilter = async () => {
        await getData();
    }

    const handleResetFilters = async () => {
        setFilters({title: ''});
        await getData(pagination.page, pagination.limit, {title: ''});
    };

    const createOrUpdateTopic = async (topicId, body) => {
        let api
        if (topicId)
            api = await topicService.updateTopic(topicId, body)
        else
            api = await topicService.createTopic(body)
        if (api) {
            setId(null)
            setDialogOpen(false)
            getData().then()
            enhancedToast.success(`${topicId ? ' Cập nhật' : 'Thêm mới'} chủ đề thành công!`)
        }
    }

    const onClickEdit = (topicId) => {
        setId(topicId)
        setDialogOpen(true)
    }

    const handleCancel = () => {
        setId(null)
        setDialogOpen(false);
    };


    return (<div className="space-y-6">
            <div className="mx-auto">
                <div className="flex justify-between items-center mb-6">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Chủ đề hỗ trợ</h1>
                        <p className="text-muted-foreground">
                            Quản lý các chủ đề hỗ trợ tự động và hướng dẫn cách hoạt động cho chatbot.
                        </p>
                    </div>

                    <Button onClick={() => setDialogOpen(true)}>
                        <Plus className="mr-1 h-4 w-4"/>
                        Thêm mới
                    </Button>
                </div>

                {/* Filter Section */}
                <div className="bg-card rounded-lg border p-4 mb-6">
                    <div className="flex flex-col sm:flex-row gap-4 items-end">

                        <div className="w-full sm:w-96">
                            <label className="text-sm font-medium mb-2 block">Tên chủ đề</label>
                            <div className="flex gap-2">
                                <Input
                                    placeholder="Nhập tên chủ đề..."
                                    className="flex-1"
                                    value={filters.title}
                                    onChange={(e) => {
                                        setFilters(prevState => {
                                            return {
                                                ...prevState,
                                                title: e.target.value
                                            }
                                        })
                                    }}
                                />
                            </div>
                        </div>

                        <div className="w-full sm:w-48 flex gap-2">
                            <Button
                                variant="outline"
                                onClick={() => handleFilter()}
                            >
                                Lọc
                            </Button>

                            <Button
                                variant="outline"
                                onClick={() => handleResetFilters()}
                            >
                                Đặt lại
                            </Button>
                        </div>


                    </div>
                </div>

                <div className="bg-card rounded-lg border">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Tên chủ đề</TableHead>
                                <TableHead>Mô tả</TableHead>
                                <TableHead>Ngày tạo</TableHead>
                                <TableHead className="text-right w-[150px]">Thao tác</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {topics.map((topic) => (
                                <TableRow key={topic._id}>
                                    <TableCell className="font-medium">
                                        {topic.title}
                                    </TableCell>
                                    <TableCell>{topic.description}</TableCell>
                                    <TableCell className="w-[200px]">{formatDate(topic.createdAt)}</TableCell>
                                    <TableCell className="w-[150px]">
                                        <div className="flex justify-end gap-3">
                                            <Button onClick={() => onClickEdit(topic._id)} variant="default" size="xs">
                                                <SquarePen className="h-4 w-4"/>
                                            </Button>
                                            <AlertDialog>
                                                <AlertDialogTrigger asChild>
                                                    <Button variant="destructive" size="xs">
                                                        <Trash2 className="h-4 w-4"/>
                                                    </Button>
                                                </AlertDialogTrigger>
                                                <AlertDialogContent>
                                                    <AlertDialogHeader>
                                                        <AlertDialogTitle>Xác nhận xóa</AlertDialogTitle>
                                                        <AlertDialogDescription>
                                                            Bạn có chắc chắn muốn xóa chủ đề "{topic.title}"?
                                                            <br/>
                                                            Hành động này không thể hoàn tác.
                                                        </AlertDialogDescription>
                                                    </AlertDialogHeader>
                                                    <AlertDialogFooter>
                                                        <AlertDialogCancel>Hủy</AlertDialogCancel>
                                                        <AlertDialogAction
                                                            onClick={() => handleDelete(topic._id)}
                                                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                                        >
                                                            Xóa
                                                        </AlertDialogAction>
                                                    </AlertDialogFooter>
                                                </AlertDialogContent>
                                            </AlertDialog>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>

                {pagination.totalPage > 1 && (
                    <div className="flex justify-center mt-6">
                        <PaginationCustom
                            onchangePagination={onchangePagination}
                            pagination={pagination}
                        />
                    </div>
                )}

                <DialogTopic
                    _id={id}
                    open={dialogOpen}
                    handleCancel={handleCancel}
                    handleOk={createOrUpdateTopic}
                />
            </div>
        </div>
    );
};

export default Topic;