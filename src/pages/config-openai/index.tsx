import React, {useState} from 'react';
import {DefaultLayout} from "@/components/layout/DefaultLayout";
import {SEO} from "@/components/SEO.tsx";
import {Tabs, TabsList, TabsTrigger, TabsContent} from "@/components/ui/tabs";
import OpenAI from "@/pages/config-openai/components/OpenAI.tsx";
import Topic from "@/pages/config-openai/components/Topic.tsx";

const ConfigurationForm: React.FC = () => {
    const [activeTab, setActiveTab] = useState("openai");

    const handleTabChange = (value: string) => {
        setActiveTab(value);
    };

    return (
        <DefaultLayout>
            <SEO title="Cấu hình trò chuyện" description="Quản lý các thông số và cài đặt cho chatbot của bạn"/>
            <div className="space-y-6">
                <Tabs
                    value={activeTab}
                    onValueChange={handleTabChange}
                    className="w-full"
                >
                    <TabsList className="grid w-[400px] grid-cols-2">
                        <TabsTrigger
                            value="openai"
                            className="data-[state=active]:border-b-2 data-[state=active]:border-primary"
                        >
                            Thiết lập chatbot
                        </TabsTrigger>
                        <TabsTrigger
                            value="topic"
                            className="data-[state=active]:border-b-2 data-[state=active]:border-primary"
                        >
                            Chủ đề hỗ trợ
                        </TabsTrigger>
                    </TabsList>

                    <TabsContent value="openai" className="mt-6">
                        <OpenAI/>
                    </TabsContent>

                    <TabsContent value="topic" className="mt-6">
                        <Topic/>
                    </TabsContent>
                </Tabs>
            </div>
        </DefaultLayout>
    );
};

export default ConfigurationForm;