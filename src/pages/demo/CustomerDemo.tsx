import { useState } from "react";
import { CustomerLayout } from "@/components/layout/CustomerLayout";
import { SEO } from "@/components/SEO";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import { 
  PhoneCall, 
  MessageSquare, 
  History, 
  Clock,
  CheckCircle,
  AlertCircle,
  Package,
  User,
  Settings
} from "lucide-react";

export default function CustomerDemo() {
  const navigate = useNavigate();
  const [userRole, setUserRole] = useState<'shop' | 'buyer'>('shop');

  const basePath = userRole === 'buyer' ? '/buyer' : '/shop';
  const userTitle = userRole === 'buyer' ? 'Người mua' : 'Cửa hàng';

  const quickStats = [
    {
      id: 'pending-orders',
      title: '<PERSON><PERSON>n hàng chờ xử lý',
      value: '3',
      description: '<PERSON><PERSON>n theo dõi',
      icon: <Clock className="h-5 w-5" />,
      color: 'text-yellow-600'
    },
    {
      id: 'completed-orders',
      title: 'Đơn hàng hoàn thành',
      value: '27',
      description: 'Tháng này',
      icon: <CheckCircle className="h-5 w-5" />,
      color: 'text-green-600'
    },
    {
      id: 'support-tickets',
      title: 'Yêu cầu hỗ trợ',
      value: '1',
      description: 'Đang xử lý',
      icon: <AlertCircle className="h-5 w-5" />,
      color: 'text-orange-600'
    }
  ];

  const recentActivities = [
    {
      id: '1',
      type: 'order',
      title: 'Đơn hàng #DH001234',
      description: 'Đã được giao thành công',
      time: '2 giờ trước',
      status: 'completed'
    },
    {
      id: '2',
      type: 'support',
      title: 'Yêu cầu hỗ trợ #SP001',
      description: 'Đang chờ phản hồi từ CSKH',
      time: '1 ngày trước',
      status: 'pending'
    },
    {
      id: '3',
      type: 'call',
      title: 'Cuộc gọi CSKH',
      description: 'Đã hoàn thành - 5 phút 32 giây',
      time: '2 ngày trước',
      status: 'completed'
    }
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'order':
        return <Package className="h-4 w-4" />;
      case 'support':
        return <MessageSquare className="h-4 w-4" />;
      case 'call':
        return <PhoneCall className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="secondary" className="text-green-600 bg-green-50">Hoàn thành</Badge>;
      case 'pending':
        return <Badge variant="secondary" className="text-yellow-600 bg-yellow-50">Đang xử lý</Badge>;
      default:
        return <Badge variant="secondary">Không xác định</Badge>;
    }
  };

  return (
    <CustomerLayout>
      <SEO 
        title={`Demo Giao diện - ${userTitle}`} 
        description="Demo giao diện mới cho khách hàng" 
      />
      
      <div className="space-y-6">
        {/* Role Switcher */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Demo Controls
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              <Button
                variant={userRole === 'shop' ? 'default' : 'outline'}
                onClick={() => setUserRole('shop')}
              >
                <User className="h-4 w-4 mr-2" />
                Shop Role
              </Button>
              <Button
                variant={userRole === 'buyer' ? 'default' : 'outline'}
                onClick={() => setUserRole('buyer')}
              >
                <User className="h-4 w-4 mr-2" />
                Buyer Role
              </Button>
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              Hiện tại đang xem với role: <strong>{userTitle}</strong>
            </p>
          </CardContent>
        </Card>

        {/* Welcome Section */}
        <div className="text-center py-8">
          <h1 className="text-3xl font-medium text-foreground mb-4">
            Demo Giao diện CSKH Nhẹ nhàng
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Giao diện đã được thiết kế nhẹ nhàng, sử dụng logo GHVN chung với admin
          </p>
        </div>

        {/* Key Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="bg-blue-50 border-blue-100 border-2">
            <CardContent className="p-6 text-center">
              <PhoneCall className="h-10 w-10 mx-auto mb-4 text-blue-600" />
              <h3 className="text-lg font-semibold text-blue-900 mb-2">Nhẹ nhàng</h3>
              <p className="text-blue-700 text-sm">
                Soft colors, subtle shadows, gentle hover effects
              </p>
            </CardContent>
          </Card>

          <Card className="bg-green-50 border-green-100 border-2">
            <CardContent className="p-6 text-center">
              <MessageSquare className="h-10 w-10 mx-auto mb-4 text-green-600" />
              <h3 className="text-lg font-semibold text-green-900 mb-2">Thống nhất</h3>
              <p className="text-green-700 text-sm">
                Logo GHVN chung với admin, consistent branding
              </p>
            </CardContent>
          </Card>

          <Card className="bg-purple-50 border-purple-100 border-2">
            <CardContent className="p-6 text-center">
              <History className="h-10 w-10 mx-auto mb-4 text-purple-600" />
              <h3 className="text-lg font-semibold text-purple-900 mb-2">Tối giản</h3>
              <p className="text-purple-700 text-sm">
                Clean design, minimal elements, focus on functionality
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Demo Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg text-center">Test giao diện mới</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <Button
                variant="outline"
                className="h-auto py-6 flex flex-col gap-3 hover:shadow-lg transition-all"
                onClick={() => navigate(`${basePath}/dashboard`)}
              >
                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                  <Package className="h-6 w-6 text-blue-600" />
                </div>
                <div className="text-center">
                  <div className="font-semibold">Trang chủ mới</div>
                  <div className="text-xs text-muted-foreground">Giao diện đơn giản</div>
                </div>
              </Button>

              <Button
                variant="outline"
                className="h-auto py-6 flex flex-col gap-3 hover:shadow-lg transition-all"
                onClick={() => navigate(`${basePath}/call`)}
              >
                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                  <PhoneCall className="h-6 w-6 text-green-600" />
                </div>
                <div className="text-center">
                  <div className="font-semibold">Call Center</div>
                  <div className="text-xs text-muted-foreground">WebRTC SIP</div>
                </div>
              </Button>

              <Button
                variant="outline"
                className="h-auto py-6 flex flex-col gap-3 hover:shadow-lg transition-all"
                onClick={() => navigate(`${basePath}/support`)}
              >
                <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
                  <MessageSquare className="h-6 w-6 text-purple-600" />
                </div>
                <div className="text-center">
                  <div className="font-semibold">Real-time Chat</div>
                  <div className="text-xs text-muted-foreground">Socket.IO</div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Summary */}
        <Card className="bg-slate-50 border-slate-200 border-2">
          <CardContent className="p-6 text-center">
            <h3 className="text-lg font-semibold text-slate-900 mb-2">
              ✨ Giao diện nhẹ nhàng và thống nhất
            </h3>
            <p className="text-slate-700 mb-4">
              Logo GHVN chung với admin, soft colors, gentle animations, minimal design
            </p>
            <div className="flex flex-wrap justify-center gap-2 text-sm">
              <Badge variant="secondary">Logo GHVN</Badge>
              <Badge variant="secondary">Soft colors</Badge>
              <Badge variant="secondary">Gentle effects</Badge>
              <Badge variant="secondary">Minimal design</Badge>
            </div>
          </CardContent>
        </Card>
      </div>
    </CustomerLayout>
  );
}
