import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatTime(dateString: string) {
  const date = new Date(dateString);
  return date.toLocaleTimeString('vi-VN', {
    hour: '2-digit',
    minute: '2-digit'
  });
}

export function formatDate(dateString: string) {
  const date = new Date(dateString);
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  if (date.toDateString() === today.toDateString()) {
    return 'Hôm nay';
  } else if (date.toDateString() === yesterday.toDateString()) {
    return 'Hôm qua';
  } else {
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }
}

export const playNotificationSound = async () => {
  const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
  if (!AudioContext) return;

  const audioContext = new AudioContext();
  if (audioContext.state === 'suspended') {
    await audioContext.resume();
  }

  const gain = audioContext.createGain();
  gain.connect(audioContext.destination);

  const playTone = (freq: number, start: number, duration: number) => {
    const osc = audioContext.createOscillator();
    osc.type = 'sine';
    osc.frequency.value = freq;

    osc.connect(gain);
    gain.gain.setValueAtTime(0.05, audioContext.currentTime + start);
    gain.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + start + duration);

    osc.start(audioContext.currentTime + start);
    osc.stop(audioContext.currentTime + start + duration);
  };

  // Ding (nốt D5)
  playTone(587.33, 0, 0.12);
  // Dong (nốt G5) sau 150ms
  playTone(783.99, 0.15, 0.12);
};

