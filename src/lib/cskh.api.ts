import {enhancedToast} from "@/components/common/EnhancedToast.tsx";

type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

interface ApiRequestOptions {
  method?: HttpMethod;
  params?: Record<string, any>;
  body?: any;
  headers?: Record<string, string>;
}

const BASE_URL = import.meta.env.VITE_CHAT_URL || '/cskh';

export async function apiRequest<T = any>(
  endpoint: string,
  options: ApiRequestOptions = {}
): Promise<T> {
  const {method = 'GET', params, body, headers = {}} = options;

  // Tạo URL kèm query string nếu có
  let url = BASE_URL + endpoint;
  if (params) {

    const query = new URLSearchParams();
    for (const key in params) {
      if (params[key] !== undefined && params[key] !== null) {
        query.append(key, String(params[key]));
      }
    }
    url += `?${query.toString()}`;
  }

  // Tạo options cho fetch
  const fetchOptions: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
    credentials: 'include', // Cho phép gửi cookie
    redirect: 'manual'
  };

  if (body && method !== 'GET') {
    if (body instanceof FormData) {
      fetchOptions.body = body;
      delete fetchOptions.headers['Content-Type'];
    } else {
      fetchOptions.body = JSON.stringify(body);
    }
  }

  const res = await fetch(url, fetchOptions);

  if (!res.ok) {
    const errorData = await res.json().catch(() => ({}));
    enhancedToast.error(errorData.detail || "Đã có lỗi xảy ra");
    throw new Error(errorData.detail || `Lỗi API: ${res.status}`);
  }

  return res.json();
}

export const cskh_api = {
  get: <T>(url: string, params?: any) =>
    apiRequest<T>(url, {method: 'GET', params}),

  post: <T>(url: string, body?: any) =>
    apiRequest<T>(url, {method: 'POST', body}),

  put: <T>(url: string, body?: any) =>
    apiRequest<T>(url, {method: 'PUT', body}),

  delete: <T>(url: string, params?: any) =>
    apiRequest<T>(url, {method: 'DELETE', params}),
};

export interface ListResponse {
  data: []
  totalItem: number;
  currentItem: number;
  totalPage: number;
  currentPage: number;
  success?: boolean;
}

