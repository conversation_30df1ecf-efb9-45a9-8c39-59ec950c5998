/**
 * Utility để lấy cấu hình SIP dựa trên role của người dùng
 */

import { SIP_CONFIG } from '@/config/env';

export type UserRole = 'admin' | 'cskh' | 'shop' | 'buyer' | 'customer';

export interface SipCredentials {
  username: string;
  password: string;
}

/**
 * Lấy thông tin đăng nhập SIP dựa trên role
 * @param role Role của người dùng
 * @returns Thông tin đăng nhập SIP
 */
export function getSipCredentialsByRole(role: UserRole): SipCredentials {
  switch (role) {
    case 'admin':
    case 'cskh':
      // CSKH và Admin sử dụng tài khoản cskh-1
      return {
        username: SIP_CONFIG.DEFAULT_USER,
        password: SIP_CONFIG.DEFAULT_PASSWORD
      };
    
    case 'shop':
    case 'buyer':
    case 'customer':
      // Shop, Buyer và Customer sử dụng tài khoản kh-1
      return {
        username: SIP_CONFIG.CUSTOMER_USER,
        password: SIP_CONFIG.CUSTOMER_PASSWORD
      };
    
    default:
      // Mặc định sử dụng tài khoản khách hàng
      return {
        username: SIP_CONFIG.CUSTOMER_USER,
        password: SIP_CONFIG.CUSTOMER_PASSWORD
      };
  }
}

/**
 * Kiểm tra xem role có phải là CSKH/Admin không
 * @param role Role của người dùng
 * @returns true nếu là CSKH/Admin
 */
export function isStaffRole(role: UserRole): boolean {
  return role === 'admin' || role === 'cskh';
}

/**
 * Kiểm tra xem role có phải là khách hàng không
 * @param role Role của người dùng
 * @returns true nếu là khách hàng
 */
export function isCustomerRole(role: UserRole): boolean {
  return role === 'shop' || role === 'buyer' || role === 'customer';
}

/**
 * Lấy tên hiển thị cho role
 * @param role Role của người dùng
 * @returns Tên hiển thị
 */
export function getRoleDisplayName(role: UserRole): string {
  switch (role) {
    case 'admin':
      return 'Quản trị viên';
    case 'cskh':
      return 'Chăm sóc khách hàng';
    case 'shop':
      return 'Cửa hàng';
    case 'buyer':
      return 'Người mua';
    case 'customer':
      return 'Khách hàng';
    default:
      return 'Người dùng';
  }
}
