import {Navigate, Outlet, useLocation} from "react-router-dom";
import {useAuth} from "@/contexts/AuthContext";
import {LoadingScreen} from "../ui/loading-screen";

export const PrivateRoute = () => {
  const {isAuthenticated, loading, user} = useAuth();
  const location = useLocation();

  if (loading) {
    return <LoadingScreen/>;
  }

  if (!isAuthenticated) {
    // Redirect to auth page but save the current location they were trying to access
    return <Navigate to="/auth" state={{from: location}} replace/>;
  }

  // Role-based redirection for root dashboard access
  if (location.pathname === "/dashboard") {
    if (user?.role === "shop") {
      return <Navigate to="/shop/dashboard" replace/>;
    } else if (user?.role === "buyer") {
      return <Navigate to="/buyer/dashboard" replace/>;
    }
  }

  // If authenticated, render the child routes
  return (<>
      <Outlet/>
    </>


  );
};
