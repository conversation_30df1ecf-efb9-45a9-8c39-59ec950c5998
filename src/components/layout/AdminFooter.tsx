import { APP_CONFIG } from "@/config/env";
import { Separator } from "@/components/ui/separator";
import { Heart } from "lucide-react";

export function AdminFooter() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-muted/20 border-t border-border mt-auto">
      <div className="px-4 lg:px-8 py-6">
        {/* Main Footer Content */}
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          {/* Left side - Company info */}
          <div className="flex items-center gap-3">
            <div className="app-logo w-8 h-8">
              <img
                src={APP_CONFIG.LOGO}
                alt="GHVN Logo"
                className="w-full h-full object-contain"
              />
            </div>
            <div className="text-center md:text-left">
              <p className="text-sm font-medium">GHVN - <PERSON><PERSON> thống CSKH</p>
              <p className="text-xs text-muted-foreground">Phiên bản 1.0.0</p>
            </div>
          </div>

          {/* Right side - Copyright */}
          <div className="text-center md:text-right">
            <p className="text-sm text-muted-foreground">
              COPYRIGHT © {currentYear} GHVN, All rights Reserved.
            </p>
            <div className="flex items-center justify-center md:justify-end gap-1 mt-1">
              <span className="text-xs text-muted-foreground">Made with</span>
              <Heart className="h-3 w-3 text-red-500 fill-current" />
              <span className="text-xs text-muted-foreground">in Vietnam</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
