import React, {
  ButtonHTMLAttributes,
  forwardRef,
  ForwardedRef
} from 'react';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useIsMobile } from "@/hooks/use-mobile.tsx";

interface ResponsiveTooltipButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  icon: React.ReactNode;
  title: string;
  responsive?: boolean;
}

const ButtonResponsive = forwardRef<HTMLButtonElement, ResponsiveTooltipButtonProps>(
  ({ icon, title, responsive = false, ...props }, ref: ForwardedRef<HTMLButtonElement>) => {
    const isMobile = useIsMobile();

    if (isMobile && responsive)
      return (
        <Tooltip>
          <TooltipTrigger asChild>
            <button ref={ref} {...props}>
              {icon}
            </button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{title}</p>
          </TooltipContent>
        </Tooltip>
      );

    return (
      <button ref={ref} {...props}>
        {icon}
        <span>{title}</span>
      </button>
    );
  }
);

ButtonResponsive.displayName = "ButtonResponsive"; // 👈 nên có khi dùng forwardRef
export default ButtonResponsive;
