import React, { useCallback, useState } from "react";
import ReactMarkdown from "react-markdown";
import rehypeHighlight from "rehype-highlight";
import remarkGfm from "remark-gfm";
import "highlight.js/styles/github-dark.css";
import remarkMath from "remark-math";
import rehypeKatex from "rehype-katex";
import "katex/dist/katex.min.css";
import { formatMathContent } from "@/utils/commons.ts";
import remarkBreaks from "remark-breaks";
import rehypeRaw from "rehype-raw";
import { Copy, Check } from "lucide-react";
import "./index.css";

interface MarkdownRendererProps {
  children: string;
  maxWidth?: string;
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ children, maxWidth = "100%" }) => {

  const renderTable = (children: React.ReactNode) => {
    const childrenArray = React.Children.toArray(children);
    const [thead, tbody] = childrenArray;

    if (!thead || !tbody) return null;

    const headRow = React.Children.toArray((thead as any)?.props?.children)[0];
    const headCells = React.Children.toArray((headRow as any)?.props?.children);

    const bodyRows = React.Children.toArray((tbody as any)?.props?.children);

    return (
      <div className="overflow-x-auto" style={{ maxWidth }}>
        <table className="w-full border-collapse border border-gray-300">
          <thead>
          <tr>
            {headCells.map((cell: any, index) => (
              <th key={index} className="border border-gray-300 bg-gray-50 px-3 py-2 text-left font-semibold">
                {cell?.props?.children}
              </th>
            ))}
          </tr>
          </thead>
          <tbody>
          {bodyRows.map((row: any, rowIndex) => {
            const rowCells = React.Children.toArray(row?.props?.children);
            return (
              <tr key={rowIndex} className="even:bg-gray-50">
                {rowCells.map((cell: any, cellIndex) => (
                  <td key={cellIndex} className="border border-gray-300 px-3 py-2">
                    {cell?.props?.children}
                  </td>
                ))}
              </tr>
            );
          })}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <div className="markdown">
      <ReactMarkdown
        remarkPlugins={[remarkGfm, remarkMath, remarkBreaks]}
        rehypePlugins={[[rehypeHighlight, { ignoreMissing: true }], rehypeKatex, rehypeRaw]}
        components={{
          pre: (props) => <CustomPre {...props} maxWidth={maxWidth} />,
          code: CustomCodeBlock,
          h1: (props) => <h1 className="text-xl sm:text-2xl mb-4 font-bold">{props.children}</h1>,
          h2: (props) => <h2 className="text-lg sm:text-xl mb-3 font-semibold">{props.children}</h2>,
          h3: (props) => <h3 className="text-base sm:text-lg mb-2 font-medium">{props.children}</h3>,
          a: (props) => <CustomLink children={props.children} href={props.href} />,
          p: (props) => <p className="my-2 text-justify leading-relaxed">{props.children}</p>,
          ul: (props) => (
            <ul className="mb-4 ml-6 list-disc">
              {props.children}
            </ul>
          ),
          ol: ({ children, ...props }) => (
            <ol start={props.start} className="mb-4 ml-6 list-decimal">
              {children}
            </ol>
          ),
          li: (props) => <li className="mb-1">{props.children}</li>,
          img: (props) => {
            const { src, alt, title } = props;
            return (
              <div className="flex items-center justify-center my-4">
                <img
                  className="max-w-full h-auto rounded-lg shadow-sm"
                  src={src}
                  alt={alt}
                  title={title}
                  style={{ maxWidth: '300px' }}
                />
              </div>
            );
          },
          table: (props) => renderTable(props.children),
        }}
      >
        {formatMathContent(children)}
      </ReactMarkdown>
    </div>
  );
};

const CustomPre: React.FC<{ children: React.ReactNode; maxWidth?: string }> = ({ children, maxWidth }) => {
  const [isCopied, setIsCopied] = useState(false);
  const code = React.Children.toArray(children).find(isValidCustomCodeBlock);
  const language = code && (code as any).props.className
    ? extractLanguageName((code as any).props.className.replace("hljs ", ""))
    : "";

  const handleCopyClick = useCallback(() => {
    if (code && React.isValidElement(code)) {
      const codeString = extractTextFromNode(code.props.children);
      navigator.clipboard.writeText(codeString);
      setIsCopied(true);
      setTimeout(() => {
        setIsCopied(false);
      }, 2000);
    }
  }, [code]);

  return (
    <div className="mb-4" style={{ maxWidth }}>
      <div className="flex flex-col md:flex-row justify-between items-center bg-gray-800 p-3 rounded-t-lg">
        <div className="font-mono text-white text-sm">
          {language.charAt(0).toUpperCase() + language.slice(1)}
        </div>
        <button
          onClick={handleCopyClick}
          className="flex items-center gap-2 rounded px-3 py-1 text-white hover:bg-gray-700 transition-colors"
        >
          {isCopied ? <Check size={16} /> : <Copy size={16} />}
          {isCopied ? "Đã sao chép" : "Sao chép"}
        </button>
      </div>
      <pre className="rounded-t-none m-0">{children}</pre>
    </div>
  );
};

const CustomCodeBlock: React.FC<{ inline?: boolean; className?: string; children: React.ReactNode }> = ({
                                                                                                          inline,
                                                                                                          className,
                                                                                                          children
                                                                                                        }) => {
  if (inline) {
    return <code className="bg-gray-100 rounded px-1 py-0.5 text-sm font-mono">{children}</code>;
  }
  const language = className ? className.replace("language-", "") : "plaintext";
  return <code className={`hljs ${language}`}>{children}</code>;
};

const extractLanguageName = (languageString: string): string => {
  const parts = languageString.split("-");
  if (parts.length > 1) {
    return parts[1] || "";
  }
  return "";
};

const isValidCustomCodeBlock = (element: any): boolean => {
  return React.isValidElement(element) && element.type === CustomCodeBlock;
};

const extractTextFromNode = (node: React.ReactNode): string => {
  if (typeof node === "string") {
    return node;
  }
  if (Array.isArray(node)) {
    return node.map(extractTextFromNode).join("");
  }
  if (React.isValidElement(node)) {
    return extractTextFromNode(node.props.children);
  }
  return "";
};

const isNumber = (value: any): boolean => {
  if (!value || !Array.isArray(value) || value.length === 0) return false;
  return !isNaN(value[0].replace(/[()]/g, ""));
};

const CustomLink: React.FC<{ children: React.ReactNode; href?: string }> = ({ children, href }) => {
  const isNumberLink = isNumber(children);

  return (
    <a
      className={isNumberLink ? "p-quote-link" : "p-no-quote-link"}
      href={href}
      target="_blank"
      rel="noopener noreferrer"
    >
      {isNumberLink ? (children as any)[0].replace(/[()]/g, "") : children}
    </a>
  );
};

export default MarkdownRenderer;